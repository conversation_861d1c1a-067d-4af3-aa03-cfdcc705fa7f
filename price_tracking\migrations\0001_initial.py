# Generated by Django 5.2.1 on 2025-06-25 13:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Store',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('website', models.URLField(blank=True)),
                ('location', models.CharField(blank=True, max_length=200)),
                ('is_online', models.BooleanField(default=True)),
                ('is_physical', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='PriceAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('target_price', models.DecimalField(decimal_places=3, max_digits=10)),
                ('currency', models.CharField(default='TND', max_length=3)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('triggered_at', models.DateTimeField(blank=True, null=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProductPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price', models.DecimalField(decimal_places=3, max_digits=10)),
                ('currency', models.CharField(default='TND', max_length=3)),
                ('is_on_sale', models.BooleanField(default=False)),
                ('original_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True)),
                ('discount_percentage', models.FloatField(blank=True, null=True)),
                ('availability', models.CharField(default='in_stock', max_length=50)),
                ('product_url', models.URLField(blank=True)),
                ('date_recorded', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prices', to='products.product')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='price_tracking.store')),
            ],
            options={
                'ordering': ['-date_recorded'],
                'indexes': [models.Index(fields=['product', '-date_recorded'], name='price_track_product_e2b9bd_idx'), models.Index(fields=['store', '-date_recorded'], name='price_track_store_i_1df453_idx')],
            },
        ),
        migrations.CreateModel(
            name='PriceHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('average_price', models.DecimalField(decimal_places=3, max_digits=10)),
                ('min_price', models.DecimalField(decimal_places=3, max_digits=10)),
                ('max_price', models.DecimalField(decimal_places=3, max_digits=10)),
                ('price_trend', models.CharField(max_length=20)),
                ('period_start', models.DateField()),
                ('period_end', models.DateField()),
                ('currency', models.CharField(default='TND', max_length=3)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='price_tracking.store')),
            ],
            options={
                'unique_together': {('product', 'store', 'period_start', 'period_end')},
            },
        ),
    ]

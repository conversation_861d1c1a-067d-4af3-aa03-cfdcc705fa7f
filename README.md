# SkinCare Tunisia - Analyseur de Type de Peau

## 🌟 Description

SkinCare Tunisia est une application web Django qui permet aux utilisateurs d'analyser leur type de peau et de recevoir des recommandations personnalisées de produits cosmétiques disponibles en Tunisie. L'application utilise l'intelligence artificielle pour analyser la peau via deux méthodes : formulaire interactif ou analyse par caméra en temps réel.

## ✨ Fonctionnalités Principales

### 🔍 Analyse de Type de Peau

- **Analyse par Formulaire** : Questionnaire intelligent avec questions ciblées
- **Analyse par Caméra** : Capture et analyse en temps réel du visage
- **Support Téléphone** : Possibilité d'utiliser son téléphone comme caméra via USB
- **IA Avancée** : Algorithmes de vision par ordinateur avec détection précise des imperfections
- **Détection d'Acné** : Identification spécialisée de la peau acnéique avec comptage des imperfections
- **Analyse Multi-Zones** : Comparaison zone T vs joues pour détecter la peau mixte
- **Confiance Ajustée** : Pourcentage de confiance basé sur la qualité d'image et les critères détectés

### 📱 Types de Peau Détectés

- Peau Grasse
- Peau Sèche
- Peau Mixte
- Peau Sensible
- Peau Normale
- Peau à Tendance Acnéique

### 🛍️ Recommandations Produits

- Produits de marques disponibles en Tunisie
- Recommandations personnalisées selon le type de peau
- Informations détaillées sur chaque produit
- Conseils d'utilisation et ingrédients

### 💰 Suivi des Prix

- Historique des prix en temps réel
- Alertes de prix personnalisées
- Comparaison entre différents magasins
- Détection des meilleures offres

## 🚀 Installation

### Prérequis

- Python 3.8+
- pip
- Git

### Installation Rapide

```bash
# Cloner le projet
git clone <repository-url>
cd peau

# Installer les dépendances
pip install django pillow opencv-python numpy tensorflow scikit-learn matplotlib seaborn pandas requests beautifulsoup4

# Appliquer les migrations
python manage.py migrate

# Initialiser les données de test
python manage.py init_data

# Créer un superutilisateur (optionnel)
python manage.py createsuperuser

# Lancer le serveur
python manage.py runserver
```

L'application sera accessible à l'adresse : http://127.0.0.1:8000/

## 📋 Utilisation

### 1. Analyse par Formulaire

1. Accédez à la page d'accueil
2. Cliquez sur "Analyser par Formulaire"
3. Répondez aux questions sur votre peau
4. Optionnel : Uploadez une photo de votre visage
5. Obtenez vos résultats et recommandations

### 2. Analyse par Caméra

1. Cliquez sur "Analyser par Caméra"
2. Autorisez l'accès à votre caméra
3. Positionnez votre visage dans le cadre
4. Cliquez sur le bouton de capture
5. Obtenez une analyse instantanée

### 3. Utiliser son Téléphone comme Caméra

L'application inclut maintenant un **guide complet intégré** pour connecter votre téléphone comme caméra !

#### Accès au Guide :

- Cliquez sur "Guide Téléphone" dans la page d'analyse par caméra
- Ou visitez directement : `/help/phone-camera/`

#### Android (DroidCam - Recommandé) :

1. **Activation développeur** : Paramètres → À propos → Appuyez 7x sur "Numéro de build"
2. **Débogage USB** : Paramètres → Options développeur → Activez "Débogage USB"
3. **Installation** : Téléchargez "DroidCam" (Play Store + PC)
4. **Connexion** : Connectez via USB, lancez les deux apps, sélectionnez "USB"

#### iPhone (EpocCam - Recommandé) :

1. **Installation** : Téléchargez "EpocCam" (App Store + PC)
2. **Connexion** : Connectez via câble Lightning/USB-C
3. **Activation** : Lancez les deux apps, autorisez la connexion

#### Détection Automatique :

- L'application détecte automatiquement les téléphones connectés
- Identification du type de caméra (DroidCam, EpocCam, etc.)
- Bouton "Tester la caméra" pour vérifier la connexion
- Messages d'aide contextuels en cas de problème

## 🏗️ Architecture

### Applications Django

- **skin_analysis** : Analyse de peau et gestion des utilisateurs
- **products** : Gestion des produits et recommandations
- **price_tracking** : Suivi des prix et alertes

### Technologies Utilisées

- **Backend** : Django 5.2, Python
- **Frontend** : Bootstrap 5, JavaScript ES6
- **IA/ML** : OpenCV, NumPy, TensorFlow
- **Base de données** : SQLite (développement)
- **Styles** : CSS3 avec animations

## 🧪 Tests

```bash
# Lancer tous les tests
python manage.py test

# Tests avec verbosité
python manage.py test --verbosity=2

# Tests d'une application spécifique
python manage.py test skin_analysis
```

## 📊 Modèles de Données

### SkinType

- Types de peau avec descriptions et conseils
- Caractéristiques spécifiques (JSON)

### SkinAnalysis

- Historique des analyses utilisateur
- Méthode d'analyse (formulaire/caméra)
- Score de confiance et données détaillées

### Product

- Produits cosmétiques avec marques tunisiennes
- Types de peau compatibles
- Ingrédients et instructions d'usage

### ProductPrice

- Historique des prix par magasin
- Détection des promotions
- Suivi de disponibilité

## 🔧 Configuration Avancée

### Variables d'Environnement

Créez un fichier `.env` pour la production :

```env
DEBUG=False
SECRET_KEY=your-secret-key
DATABASE_URL=your-database-url
ALLOWED_HOSTS=your-domain.com
```

### Optimisation Caméra

Pour améliorer la détection caméra :

1. Utilisez Chrome ou Firefox (recommandé)
2. Activez HTTPS en production
3. Configurez les permissions caméra du navigateur

## 🤝 Contribution

1. Fork le projet
2. Créez une branche feature (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

### Problèmes Courants

**Caméra ne fonctionne pas :**

- Vérifiez les permissions du navigateur
- Fermez les autres applications utilisant la caméra
- Essayez un autre navigateur
- Connectez votre téléphone via USB

**Erreur d'analyse d'image :**

- Vérifiez que OpenCV est installé
- Assurez-vous que l'image est de bonne qualité
- Utilisez un éclairage naturel

**Produits non affichés :**

- Lancez `python manage.py init_data` pour créer les données de test
- Vérifiez la base de données

### Contact

Pour toute question ou suggestion, ouvrez une issue sur GitHub.

---

Développé avec ❤️ pour la communauté tunisienne

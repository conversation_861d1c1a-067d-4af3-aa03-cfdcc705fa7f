from django.db import models
from products.models import Product
from django.contrib.auth.models import User

class Store(models.Model):
    """Magasins/sites où les produits sont vendus"""
    name = models.CharField(max_length=100, unique=True)
    website = models.URLField(blank=True)
    location = models.CharField(max_length=200, blank=True)  # Adresse physique
    is_online = models.BooleanField(default=True)
    is_physical = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

class ProductPrice(models.Model):
    """Historique des prix des produits"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='prices')
    store = models.ForeignKey(Store, on_delete=models.CASCADE)
    price = models.DecimalField(max_digits=10, decimal_places=3)  # Prix en dinars tunisiens
    currency = models.CharField(max_length=3, default='TND')
    is_on_sale = models.BooleanField(default=False)
    original_price = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    discount_percentage = models.FloatField(null=True, blank=True)
    availability = models.CharField(max_length=50, default='in_stock')
    product_url = models.URLField(blank=True)
    date_recorded = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-date_recorded']
        indexes = [
            models.Index(fields=['product', '-date_recorded']),
            models.Index(fields=['store', '-date_recorded']),
        ]

    def __str__(self):
        return f"{self.product.name} - {self.price} {self.currency} chez {self.store.name}"

class PriceAlert(models.Model):
    """Alertes de prix pour les utilisateurs"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    target_price = models.DecimalField(max_digits=10, decimal_places=3)
    currency = models.CharField(max_length=3, default='TND')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    triggered_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Alerte {self.user.username} - {self.product.name} <= {self.target_price} {self.currency}"

class PriceHistory(models.Model):
    """Statistiques et historique des prix"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    store = models.ForeignKey(Store, on_delete=models.CASCADE)
    average_price = models.DecimalField(max_digits=10, decimal_places=3)
    min_price = models.DecimalField(max_digits=10, decimal_places=3)
    max_price = models.DecimalField(max_digits=10, decimal_places=3)
    price_trend = models.CharField(max_length=20)  # 'increasing', 'decreasing', 'stable'
    period_start = models.DateField()
    period_end = models.DateField()
    currency = models.CharField(max_length=3, default='TND')

    class Meta:
        unique_together = ['product', 'store', 'period_start', 'period_end']

    def __str__(self):
        return f"Historique {self.product.name} - {self.store.name} ({self.period_start} - {self.period_end})"

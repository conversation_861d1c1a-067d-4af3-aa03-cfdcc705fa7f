{% extends 'skin_analysis/base.html' %}

{% block title %}Tableau de Bord des Prix - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="text-white">
                <i class="fas fa-chart-line"></i> Tableau de Bord des Prix
            </h2>
            <p class="text-white-50">Suivez les prix et trouvez les meilleures offres</p>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-body">
                    <i class="fas fa-search fa-2x text-primary mb-2"></i>
                    <h6>Rechercher Produits</h6>
                    <a href="{% url 'products:list' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-arrow-right"></i> Parcourir
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-body">
                    <i class="fas fa-bell fa-2x text-warning mb-2"></i>
                    <h6>Mes Alertes</h6>
                    <a href="{% url 'price_tracking:alerts' %}" class="btn btn-warning btn-sm">
                        <i class="fas fa-arrow-right"></i> Gérer
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-body">
                    <i class="fas fa-star fa-2x text-success mb-2"></i>
                    <h6>Recommandations</h6>
                    {% if user.is_authenticated and user.userprofile.skin_type %}
                    <a href="{% url 'products:recommendations' user.userprofile.skin_type.name %}" class="btn btn-success btn-sm">
                        <i class="fas fa-arrow-right"></i> Voir
                    </a>
                    {% else %}
                    <a href="{% url 'skin_analysis:form_analysis' %}" class="btn btn-success btn-sm">
                        <i class="fas fa-arrow-right"></i> Analyser
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-body">
                    <i class="fas fa-building fa-2x text-info mb-2"></i>
                    <h6>Marques</h6>
                    <a href="{% url 'products:brands' %}" class="btn btn-info btn-sm">
                        <i class="fas fa-arrow-right"></i> Explorer
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Best Deals Section -->
    {% if best_deals %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="card" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-header">
                    <h5><i class="fas fa-fire text-danger"></i> Meilleures Offres du Moment</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for deal in best_deals %}
                        <div class="col-lg-6 mb-3">
                            <div class="card border-danger h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="card-title">{{ deal.product.name }}</h6>
                                            <p class="text-muted small mb-2">
                                                <i class="fas fa-building"></i> {{ deal.product.brand.name }}
                                            </p>
                                            <p class="text-muted small mb-2">
                                                <i class="fas fa-store"></i> {{ deal.store.name }}
                                            </p>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-danger mb-2">
                                                -{{ deal.discount_percentage|floatformat:0 }}%
                                            </span>
                                            <div>
                                                {% if deal.original_price %}
                                                <small class="text-muted text-decoration-line-through">
                                                    {{ deal.original_price|floatformat:3 }} TND
                                                </small>
                                                <br>
                                                {% endif %}
                                                <strong class="text-danger h5">
                                                    {{ deal.price|floatformat:3 }} TND
                                                </strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <a href="{% url 'price_tracking:product_history' deal.product.id %}" 
                                           class="btn btn-outline-primary btn-sm me-2">
                                            <i class="fas fa-chart-line"></i> Historique
                                        </a>
                                        {% if deal.product_url %}
                                        <a href="{{ deal.product_url }}" target="_blank" 
                                           class="btn btn-danger btn-sm">
                                            <i class="fas fa-external-link-alt"></i> Acheter
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> Offres mises à jour quotidiennement
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Recent Price Updates -->
    {% if recent_prices %}
    <div class="row">
        <div class="col-12">
            <div class="card" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-header">
                    <h5><i class="fas fa-clock"></i> Dernières Mises à Jour de Prix</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Marque</th>
                                    <th>Magasin</th>
                                    <th>Prix</th>
                                    <th>Statut</th>
                                    <th>Mise à jour</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for price in recent_prices %}
                                <tr>
                                    <td>
                                        <strong>{{ price.product.name }}</strong>
                                        {% if price.product.volume %}
                                        <br><small class="text-muted">{{ price.product.volume }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ price.product.brand.name }}</td>
                                    <td>
                                        {{ price.store.name }}
                                        {% if price.store.is_online %}
                                        <span class="badge bg-info">En ligne</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if price.is_on_sale %}
                                        <div>
                                            {% if price.original_price %}
                                            <small class="text-muted text-decoration-line-through">
                                                {{ price.original_price|floatformat:3 }} TND
                                            </small>
                                            <br>
                                            {% endif %}
                                            <strong class="text-danger">
                                                {{ price.price|floatformat:3 }} TND
                                            </strong>
                                        </div>
                                        {% else %}
                                        <strong class="text-success">
                                            {{ price.price|floatformat:3 }} TND
                                        </strong>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if price.is_on_sale %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-fire"></i> Promo
                                        </span>
                                        {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Normal
                                        </span>
                                        {% endif %}
                                        
                                        {% if price.availability == 'in_stock' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Dispo
                                        </span>
                                        {% else %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-exclamation"></i> Limité
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ price.date_recorded|date:"d/m H:i" }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'price_tracking:product_history' price.product.id %}" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-chart-line"></i>
                                            </a>
                                            {% if user.is_authenticated %}
                                            <a href="{% url 'price_tracking:create_alert' price.product.id %}" 
                                               class="btn btn-outline-warning">
                                                <i class="fas fa-bell"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- No Data -->
    <div class="row">
        <div class="col-12">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95); padding: 60px 20px;">
                <i class="fas fa-chart-line fa-5x text-muted mb-4"></i>
                <h3>Données de Prix en Cours de Collecte</h3>
                <p class="text-muted mb-4">
                    Nous collectons actuellement les données de prix des produits. Revenez bientôt pour voir les dernières offres.
                </p>
                <a href="{% url 'products:list' %}" class="btn btn-primary">
                    <i class="fas fa-shopping-bag"></i> Parcourir les Produits
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.btn-group-sm > .btn {
    border-radius: 5px;
    margin: 0 1px;
}
</style>
{% endblock %}

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from .models import SkinType, SkinAnalysis, SkinAnalysisQuestion, UserProfile
from .utils import analyze_form_responses

class SkinAnalysisTestCase(TestCase):
    def setUp(self):
        """Configuration initiale pour les tests"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Créer des types de peau
        self.skin_type_oily = SkinType.objects.create(
            name='oily',
            description='Peau grasse',
            care_tips='Utilisez des produits matifiants'
        )

        self.skin_type_dry = SkinType.objects.create(
            name='dry',
            description='Peau sèche',
            care_tips='Hydratez régulièrement'
        )

        # Créer une question de test
        self.question = SkinAnalysisQuestion.objects.create(
            question_text='Comment décririez-vous votre peau ?',
            question_type='single_choice',
            choices=['Grasse', 'Sèche', 'Normale'],
            weight=1.0,
            skin_type_mapping={
                'oily': ['Grasse'],
                'dry': ['Sèche'],
                'normal': ['Normale']
            },
            order=1
        )

    def test_home_page(self):
        """Test de la page d'accueil"""
        response = self.client.get(reverse('skin_analysis:home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Découvrez Votre Type de Peau')

    def test_form_analysis_page(self):
        """Test de la page d'analyse par formulaire"""
        response = self.client.get(reverse('skin_analysis:form_analysis'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Analyse de Votre Type de Peau')

    def test_camera_analysis_page(self):
        """Test de la page d'analyse par caméra"""
        response = self.client.get(reverse('skin_analysis:camera_analysis'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Analyse par Caméra')

    def test_user_registration(self):
        """Test d'inscription utilisateur"""
        response = self.client.post(reverse('skin_analysis:register'), {
            'username': 'newuser',
            'password1': 'complexpass123',
            'password2': 'complexpass123'
        })
        self.assertEqual(response.status_code, 302)  # Redirection après inscription
        self.assertTrue(User.objects.filter(username='newuser').exists())

    def test_form_response_analysis(self):
        """Test de l'analyse des réponses du formulaire"""
        responses = {self.question.id: 'Grasse'}
        questions = SkinAnalysisQuestion.objects.filter(id=self.question.id)

        detected_skin_type, confidence = analyze_form_responses(responses, questions)

        self.assertEqual(detected_skin_type.name, 'oily')
        self.assertGreater(confidence, 0)

    def test_skin_analysis_creation(self):
        """Test de création d'une analyse de peau"""
        analysis = SkinAnalysis.objects.create(
            user=self.user,
            method='form',
            detected_skin_type=self.skin_type_oily,
            confidence_score=0.8,
            form_responses={'question_1': 'Grasse'}
        )

        self.assertEqual(analysis.user, self.user)
        self.assertEqual(analysis.detected_skin_type, self.skin_type_oily)
        self.assertEqual(analysis.confidence_score, 0.8)

    def test_user_profile_creation(self):
        """Test de création du profil utilisateur"""
        profile = UserProfile.objects.create(
            user=self.user,
            age=25,
            skin_type=self.skin_type_oily
        )

        self.assertEqual(profile.user, self.user)
        self.assertEqual(profile.skin_type, self.skin_type_oily)
        self.assertEqual(profile.age, 25)

    def test_protected_profile_view(self):
        """Test que la vue profil nécessite une authentification"""
        response = self.client.get(reverse('skin_analysis:profile'))
        self.assertEqual(response.status_code, 302)  # Redirection vers login

        # Test avec utilisateur connecté
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('skin_analysis:profile'))
        self.assertEqual(response.status_code, 200)

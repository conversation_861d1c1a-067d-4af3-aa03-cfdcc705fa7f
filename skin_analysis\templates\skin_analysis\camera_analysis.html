{% extends 'skin_analysis/base.html' %}

{% block title %}Analyse par <PERSON>éra - SkinCare Tunisia{% endblock %}

{% block extra_css %}
<style>
    .camera-container {
        position: relative;
        max-width: 640px;
        margin: 0 auto;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }
    
    #videoElement {
        width: 100%;
        height: auto;
        display: block;
    }
    
    .camera-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }
    
    .face-guide {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 250px;
        height: 300px;
        border: 3px solid rgba(102, 126, 234, 0.8);
        border-radius: 50%;
        background: rgba(102, 126, 234, 0.1);
    }
    
    .capture-btn {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: 4px solid white;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        pointer-events: all;
    }
    
    .capture-btn:hover {
        transform: translateX(-50%) scale(1.1);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    }
    
    .analysis-result {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-top: 30px;
        text-align: center;
    }
    
    .confidence-bar {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        height: 10px;
        overflow: hidden;
        margin: 10px 0;
    }
    
    .confidence-fill {
        background: white;
        height: 100%;
        border-radius: 10px;
        transition: width 0.5s ease;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }
    
    .tips-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .camera-controls {
        text-align: center;
        margin: 20px 0;
    }
    
    .camera-controls button {
        margin: 0 10px;
        border-radius: 25px;
        padding: 10px 20px;
    }
    
    .status-indicator {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-ready {
        background: #28a745;
        color: white;
    }
    
    .status-analyzing {
        background: #ffc107;
        color: black;
    }
    
    .status-error {
        background: #dc3545;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="text-center mb-4">
                <h2 class="text-white">
                    <i class="fas fa-camera"></i> Analyse par Caméra
                </h2>
                <p class="text-white-50">
                    Positionnez votre visage dans le cadre et cliquez pour analyser votre type de peau
                </p>
            </div>
            
            <!-- Camera Section -->
            <div class="row">
                <div class="col-md-8">
                    <div class="camera-container">
                        <video id="videoElement" autoplay muted playsinline></video>
                        <canvas id="canvasElement" style="display: none;"></canvas>
                        
                        <div class="camera-overlay">
                            <div class="face-guide"></div>
                            <button class="capture-btn" id="captureBtn" title="Capturer et analyser">
                                <i class="fas fa-camera"></i>
                            </button>
                            <div class="status-indicator status-ready" id="statusIndicator">
                                Prêt
                            </div>
                        </div>
                    </div>
                    
                    <div class="camera-controls">
                        <button class="btn btn-outline-light" id="startCameraBtn">
                            <i class="fas fa-play"></i> Démarrer la caméra
                        </button>
                        <button class="btn btn-outline-light" id="stopCameraBtn" style="display: none;">
                            <i class="fas fa-stop"></i> Arrêter la caméra
                        </button>
                        <button class="btn btn-outline-light" id="switchCameraBtn" style="display: none;">
                            <i class="fas fa-sync-alt"></i> Changer de caméra
                        </button>
                        <button class="btn btn-outline-info" id="testCameraBtn">
                            <i class="fas fa-video"></i> Tester la caméra
                        </button>
                        <button class="btn btn-outline-warning" id="helpBtn">
                            <i class="fas fa-question-circle"></i> Aide
                        </button>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="tips-card">
                        <h5><i class="fas fa-lightbulb text-warning"></i> Conseils pour une meilleure analyse</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Éclairage naturel de préférence</li>
                            <li><i class="fas fa-check text-success"></i> Visage propre, sans maquillage</li>
                            <li><i class="fas fa-check text-success"></i> Regardez directement la caméra</li>
                            <li><i class="fas fa-check text-success"></i> Restez immobile pendant l'analyse</li>
                            <li><i class="fas fa-check text-success"></i> Distance d'environ 50cm</li>
                        </ul>
                    </div>
                    
                    <div class="tips-card">
                        <h6><i class="fas fa-info-circle text-info"></i> Comment ça marche ?</h6>
                        <p class="small">
                            Notre IA analyse la texture, la couleur et les caractéristiques de votre peau 
                            pour déterminer votre type de peau avec précision.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Loading -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">Analyse en cours...</span>
                </div>
                <p class="text-white mt-2">Analyse de votre peau en cours...</p>
            </div>
            
            <!-- Results -->
            <div id="analysisResults" style="display: none;">
                <div class="analysis-result">
                    <h3><i class="fas fa-microscope"></i> Résultats de l'Analyse</h3>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5>Type de peau détecté</h5>
                            <h2 id="detectedSkinType">-</h2>
                        </div>
                        <div class="col-md-6">
                            <h5>Niveau de confiance</h5>
                            <div class="confidence-bar">
                                <div class="confidence-fill" id="confidenceFill" style="width: 0%"></div>
                            </div>
                            <span id="confidenceText">0%</span>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button class="btn btn-light btn-lg" id="getRecommendationsBtn">
                            <i class="fas fa-shopping-bag"></i> Voir les Recommandations
                        </button>
                        <button class="btn btn-outline-light btn-lg ms-2" id="retakePhotoBtn">
                            <i class="fas fa-redo"></i> Reprendre une Photo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const videoElement = document.getElementById('videoElement');
    const canvasElement = document.getElementById('canvasElement');
    const captureBtn = document.getElementById('captureBtn');
    const startCameraBtn = document.getElementById('startCameraBtn');
    const stopCameraBtn = document.getElementById('stopCameraBtn');
    const switchCameraBtn = document.getElementById('switchCameraBtn');
    const statusIndicator = document.getElementById('statusIndicator');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const analysisResults = document.getElementById('analysisResults');
    const getRecommendationsBtn = document.getElementById('getRecommendationsBtn');
    const retakePhotoBtn = document.getElementById('retakePhotoBtn');

    let currentStream = null;
    let currentDeviceId = null;
    let availableCameras = [];
    let currentCameraIndex = 0;
    let isInitialized = false;

    // Check if browser supports camera
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        updateStatus('Caméra non supportée', 'error');
        startCameraBtn.disabled = true;
        alert('Votre navigateur ne supporte pas l\'accès à la caméra. Veuillez utiliser un navigateur moderne comme Chrome, Firefox ou Safari.');
        return;
    }

    // Initialize camera detection
    initializeCamera();
    
    async function initializeCamera() {
        try {
            updateStatus('Détection...', 'analyzing');

            // Request permission first to get device labels
            try {
                const tempStream = await navigator.mediaDevices.getUserMedia({ video: true });
                tempStream.getTracks().forEach(track => track.stop());
            } catch (permissionError) {
                console.warn('Permission denied for camera access:', permissionError);
                updateStatus('Permission refusée', 'error');
                showPermissionHelp();
                return;
            }

            // Get available cameras with labels
            const devices = await navigator.mediaDevices.enumerateDevices();
            availableCameras = devices.filter(device => device.kind === 'videoinput');

            console.log('Caméras détectées:', availableCameras);

            if (availableCameras.length === 0) {
                updateStatus('Aucune caméra', 'error');
                showNoCameraHelp();
                return;
            }

            // Show camera selection if multiple cameras
            if (availableCameras.length > 1) {
                switchCameraBtn.style.display = 'inline-block';
                updateCameraList();
            }

            updateStatus('Prêt à démarrer', 'ready');
            isInitialized = true;

        } catch (error) {
            console.error('Error initializing camera:', error);
            updateStatus('Erreur d\'initialisation', 'error');
            showTroubleshootingHelp();
        }
    }

    function showPermissionHelp() {
        const helpHtml = `
            <div class="alert alert-warning mt-3">
                <h6><i class="fas fa-exclamation-triangle"></i> Permission Caméra Requise</h6>
                <p>Pour utiliser l'analyse par caméra, vous devez autoriser l'accès à votre caméra :</p>
                <ol>
                    <li>Cliquez sur l'icône de caméra dans la barre d'adresse</li>
                    <li>Sélectionnez "Autoriser" pour ce site</li>
                    <li>Rechargez la page</li>
                </ol>
                <button class="btn btn-primary btn-sm" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> Recharger la Page
                </button>
            </div>
        `;
        document.querySelector('.camera-controls').insertAdjacentHTML('afterend', helpHtml);
    }

    function showNoCameraHelp() {
        const helpHtml = `
            <div class="alert alert-info mt-3">
                <h6><i class="fas fa-info-circle"></i> Aucune Caméra Détectée</h6>
                <p>Voici comment connecter votre téléphone comme caméra :</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6>📱 Android :</h6>
                        <ul>
                            <li>Activez le "Débogage USB" dans les options développeur</li>
                            <li>Connectez via USB et sélectionnez "Transfert de fichiers"</li>
                            <li>Utilisez une app comme "DroidCam" ou "IP Webcam"</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🍎 iPhone :</h6>
                        <ul>
                            <li>Utilisez "Continuity Camera" (macOS Ventura+)</li>
                            <li>Ou une app comme "EpocCam"</li>
                            <li>Connectez via USB ou WiFi</li>
                        </ul>
                    </div>
                </div>
                <button class="btn btn-primary btn-sm" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> Rechercher à Nouveau
                </button>
            </div>
        `;
        document.querySelector('.camera-controls').insertAdjacentHTML('afterend', helpHtml);
    }

    function showTroubleshootingHelp() {
        const helpHtml = `
            <div class="alert alert-danger mt-3">
                <h6><i class="fas fa-tools"></i> Dépannage Caméra</h6>
                <p>Si vous rencontrez des problèmes :</p>
                <ul>
                    <li>Vérifiez que votre caméra n'est pas utilisée par une autre application</li>
                    <li>Redémarrez votre navigateur</li>
                    <li>Vérifiez les paramètres de confidentialité de votre navigateur</li>
                    <li>Essayez un autre navigateur (Chrome recommandé)</li>
                </ul>
                <div class="mt-2">
                    <button class="btn btn-primary btn-sm me-2" onclick="location.reload()">
                        <i class="fas fa-refresh"></i> Recharger
                    </button>
                    <a href="{% url 'skin_analysis:form_analysis' %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-clipboard-list"></i> Utiliser le Formulaire
                    </a>
                </div>
            </div>
        `;
        document.querySelector('.camera-controls').insertAdjacentHTML('afterend', helpHtml);
    }

    function updateCameraList() {
        const cameraListHtml = `
            <div class="mt-3">
                <label for="cameraSelect" class="form-label text-white">Choisir une caméra :</label>
                <select id="cameraSelect" class="form-select">
                    ${availableCameras.map((camera, index) =>
                        `<option value="${index}">${camera.label || `Caméra ${index + 1}`}</option>`
                    ).join('')}
                </select>
            </div>
        `;
        document.querySelector('.camera-controls').insertAdjacentHTML('beforeend', cameraListHtml);

        document.getElementById('cameraSelect').addEventListener('change', function(e) {
            currentCameraIndex = parseInt(e.target.value);
            currentDeviceId = availableCameras[currentCameraIndex].deviceId;
            if (currentStream) {
                stopCamera();
                startCamera();
            }
        });
    }
    
    async function startCamera() {
        if (!isInitialized) {
            alert('Veuillez d\'abord initialiser la détection des caméras.');
            return;
        }

        try {
            updateStatus('Démarrage...', 'analyzing');

            // Enhanced constraints for better compatibility
            const constraints = {
                video: {
                    width: { ideal: 1280, min: 640, max: 1920 },
                    height: { ideal: 720, min: 480, max: 1080 },
                    frameRate: { ideal: 30, min: 15, max: 60 },
                    facingMode: { ideal: 'user' }
                }
            };

            // Use specific device if selected
            if (currentDeviceId) {
                constraints.video.deviceId = { exact: currentDeviceId };
                delete constraints.video.facingMode; // Remove facingMode when using specific device
            }

            console.log('Starting camera with constraints:', constraints);

            currentStream = await navigator.mediaDevices.getUserMedia(constraints);
            videoElement.srcObject = currentStream;

            // Wait for video to be ready
            await new Promise((resolve) => {
                videoElement.onloadedmetadata = () => {
                    videoElement.play();
                    resolve();
                };
            });

            startCameraBtn.style.display = 'none';
            stopCameraBtn.style.display = 'inline-block';
            captureBtn.style.display = 'block';

            updateStatus('Caméra active', 'ready');

            // Log camera info
            const track = currentStream.getVideoTracks()[0];
            const settings = track.getSettings();
            console.log('Camera settings:', settings);

        } catch (error) {
            console.error('Error starting camera:', error);
            updateStatus('Erreur de démarrage', 'error');

            let errorMessage = 'Impossible de démarrer la caméra. ';

            if (error.name === 'NotFoundError') {
                errorMessage += 'Caméra non trouvée.';
            } else if (error.name === 'NotAllowedError') {
                errorMessage += 'Permission refusée.';
            } else if (error.name === 'NotReadableError') {
                errorMessage += 'Caméra utilisée par une autre application.';
            } else if (error.name === 'OverconstrainedError') {
                errorMessage += 'Paramètres de caméra non supportés.';
                // Try with basic constraints
                await startCameraWithBasicConstraints();
                return;
            } else {
                errorMessage += error.message;
            }

            alert(errorMessage);
        }
    }

    async function startCameraWithBasicConstraints() {
        try {
            console.log('Trying basic constraints...');
            const basicConstraints = {
                video: true
            };

            if (currentDeviceId) {
                basicConstraints.video = { deviceId: { exact: currentDeviceId } };
            }

            currentStream = await navigator.mediaDevices.getUserMedia(basicConstraints);
            videoElement.srcObject = currentStream;

            await new Promise((resolve) => {
                videoElement.onloadedmetadata = () => {
                    videoElement.play();
                    resolve();
                };
            });

            startCameraBtn.style.display = 'none';
            stopCameraBtn.style.display = 'inline-block';
            captureBtn.style.display = 'block';

            updateStatus('Caméra active (mode basique)', 'ready');

        } catch (basicError) {
            console.error('Basic camera start failed:', basicError);
            updateStatus('Échec total', 'error');
            alert('Impossible de démarrer la caméra même avec les paramètres de base.');
        }
    }
    
    function stopCamera() {
        if (currentStream) {
            currentStream.getTracks().forEach(track => track.stop());
            currentStream = null;
        }
        
        videoElement.srcObject = null;
        startCameraBtn.style.display = 'inline-block';
        stopCameraBtn.style.display = 'none';
        captureBtn.style.display = 'none';
        
        updateStatus('Arrêtée', 'error');
    }
    
    async function switchCamera() {
        if (availableCameras.length <= 1) {
            alert('Une seule caméra disponible.');
            return;
        }

        updateStatus('Changement...', 'analyzing');

        currentCameraIndex = (currentCameraIndex + 1) % availableCameras.length;
        currentDeviceId = availableCameras[currentCameraIndex].deviceId;

        console.log(`Switching to camera ${currentCameraIndex}: ${availableCameras[currentCameraIndex].label}`);

        stopCamera();

        // Small delay to ensure camera is released
        setTimeout(async () => {
            await startCamera();
        }, 500);
    }
    
    function updateStatus(text, type) {
        statusIndicator.textContent = text;
        statusIndicator.className = `status-indicator status-${type}`;
    }
    
    async function captureAndAnalyze() {
        if (!currentStream) {
            alert('Veuillez démarrer la caméra d\'abord.');
            return;
        }

        if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
            alert('La vidéo n\'est pas encore prête. Attendez quelques secondes.');
            return;
        }

        updateStatus('Capture...', 'analyzing');
        loadingSpinner.style.display = 'block';
        analysisResults.style.display = 'none';

        try {
            // Capture image from video with better quality
            const context = canvasElement.getContext('2d');
            canvasElement.width = videoElement.videoWidth;
            canvasElement.height = videoElement.videoHeight;

            // Draw video frame to canvas
            context.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);

            // Add visual feedback
            captureBtn.style.transform = 'scale(0.9)';
            setTimeout(() => {
                captureBtn.style.transform = 'scale(1)';
            }, 150);

            updateStatus('Analyse en cours...', 'analyzing');

            // Convert to blob with high quality
            canvasElement.toBlob(async (blob) => {
                if (!blob) {
                    throw new Error('Impossible de capturer l\'image');
                }

                console.log('Image captured, size:', blob.size, 'bytes');

                try {
                    const formData = new FormData();
                    formData.append('image', blob, 'capture.jpg');

                    const response = await fetch('{% url "skin_analysis:analyze_image_api" %}', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`Erreur serveur: ${response.status}`);
                    }

                    const result = await response.json();
                    console.log('Analysis result:', result);

                    if (result.error) {
                        throw new Error(result.error);
                    }

                    displayResults(result);

                } catch (error) {
                    console.error('Analysis error:', error);
                    updateStatus('Erreur d\'analyse', 'error');
                    alert('Erreur lors de l\'analyse: ' + error.message + '\n\nVeuillez réessayer ou utiliser l\'analyse par formulaire.');
                }

            }, 'image/jpeg', 0.9); // Higher quality

        } catch (error) {
            console.error('Capture error:', error);
            updateStatus('Erreur de capture', 'error');
            alert('Erreur lors de la capture: ' + error.message);
        } finally {
            loadingSpinner.style.display = 'none';
        }
    }
    
    function displayResults(result) {
        const skinTypeNames = {
            'oily': 'Peau Grasse',
            'dry': 'Peau Sèche',
            'combination': 'Peau Mixte',
            'sensitive': 'Peau Sensible',
            'normal': 'Peau Normale',
            'acne_prone': 'Peau à Tendance Acnéique'
        };
        
        document.getElementById('detectedSkinType').textContent = 
            skinTypeNames[result.skin_type] || result.skin_type;
        
        const confidence = Math.round(result.confidence * 100);
        document.getElementById('confidenceText').textContent = confidence + '%';
        document.getElementById('confidenceFill').style.width = confidence + '%';
        
        analysisResults.style.display = 'block';
        updateStatus('Analysé', 'ready');
        
        // Store result for recommendations
        analysisResults.dataset.skinType = result.skin_type;
    }
    
    // Event listeners
    startCameraBtn.addEventListener('click', startCamera);
    stopCameraBtn.addEventListener('click', stopCamera);
    switchCameraBtn.addEventListener('click', switchCamera);
    captureBtn.addEventListener('click', captureAndAnalyze);

    document.getElementById('testCameraBtn').addEventListener('click', async function() {
        updateStatus('Test en cours...', 'analyzing');
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const cameras = devices.filter(device => device.kind === 'videoinput');

            let message = `Caméras détectées: ${cameras.length}\n\n`;
            cameras.forEach((camera, index) => {
                message += `${index + 1}. ${camera.label || `Caméra ${index + 1}`}\n`;
                message += `   ID: ${camera.deviceId.substring(0, 20)}...\n\n`;
            });

            if (cameras.length === 0) {
                message += 'Aucune caméra détectée.\n\n';
                message += 'Conseils:\n';
                message += '• Vérifiez que votre caméra est connectée\n';
                message += '• Autorisez l\'accès à la caméra dans votre navigateur\n';
                message += '• Redémarrez votre navigateur\n';
                message += '• Connectez votre téléphone via USB et activez le mode caméra';
            }

            alert(message);
            updateStatus('Test terminé', 'ready');
        } catch (error) {
            alert('Erreur lors du test: ' + error.message);
            updateStatus('Erreur de test', 'error');
        }
    });

    document.getElementById('helpBtn').addEventListener('click', function() {
        const helpModal = `
            <div class="modal fade" id="helpModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-question-circle"></i> Aide - Analyse par Caméra
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-camera"></i> Utilisation de la Caméra</h6>
                                    <ul>
                                        <li>Cliquez sur "Démarrer la caméra"</li>
                                        <li>Autorisez l'accès quand demandé</li>
                                        <li>Positionnez votre visage dans le cadre</li>
                                        <li>Cliquez sur le bouton de capture</li>
                                    </ul>

                                    <h6><i class="fas fa-mobile-alt"></i> Utiliser son Téléphone</h6>
                                    <ul>
                                        <li><strong>Android:</strong> Activez "Débogage USB" et connectez via USB</li>
                                        <li><strong>iPhone:</strong> Utilisez "Continuity Camera" (macOS) ou une app comme EpocCam</li>
                                        <li>Installez une app comme "DroidCam" ou "IP Webcam"</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-exclamation-triangle"></i> Problèmes Courants</h6>
                                    <ul>
                                        <li><strong>Permission refusée:</strong> Cliquez sur l'icône caméra dans la barre d'adresse</li>
                                        <li><strong>Caméra occupée:</strong> Fermez les autres applications utilisant la caméra</li>
                                        <li><strong>Pas de caméra:</strong> Connectez une caméra externe ou votre téléphone</li>
                                        <li><strong>Image floue:</strong> Nettoyez l'objectif et améliorez l'éclairage</li>
                                    </ul>

                                    <h6><i class="fas fa-lightbulb"></i> Conseils</h6>
                                    <ul>
                                        <li>Utilisez un éclairage naturel</li>
                                        <li>Évitez les contre-jours</li>
                                        <li>Nettoyez votre visage avant l'analyse</li>
                                        <li>Restez immobile pendant la capture</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            <a href="{% url 'skin_analysis:form_analysis' %}" class="btn btn-primary">
                                Utiliser le Formulaire
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('helpModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', helpModal);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('helpModal'));
        modal.show();
    });

    retakePhotoBtn.addEventListener('click', function() {
        analysisResults.style.display = 'none';
        updateStatus('Prêt', 'ready');
    });

    getRecommendationsBtn.addEventListener('click', function() {
        const skinType = analysisResults.dataset.skinType;
        if (skinType) {
            window.location.href = `/products/recommendations/${skinType}/`;
        }
    });

    // Auto-detect cameras on page load
    setTimeout(() => {
        if (!isInitialized) {
            updateStatus('Initialisation...', 'analyzing');
            initializeCamera();
        }
    }, 1000);
});
</script>
{% endblock %}

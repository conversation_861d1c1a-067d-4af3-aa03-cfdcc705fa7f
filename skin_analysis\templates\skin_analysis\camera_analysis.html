{% extends 'skin_analysis/base.html' %}

{% block title %}Analyse par <PERSON>éra - SkinCare Tunisia{% endblock %}

{% block extra_css %}
<style>
    .camera-container {
        position: relative;
        max-width: 640px;
        margin: 0 auto;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }
    
    #videoElement {
        width: 100%;
        height: auto;
        display: block;
    }
    
    .camera-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }
    
    .face-guide {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 250px;
        height: 300px;
        border: 3px solid rgba(102, 126, 234, 0.8);
        border-radius: 50%;
        background: rgba(102, 126, 234, 0.1);
    }
    
    .capture-btn {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: 4px solid white;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        pointer-events: all;
    }
    
    .capture-btn:hover {
        transform: translateX(-50%) scale(1.1);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    }
    
    .analysis-result {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-top: 30px;
        text-align: center;
    }
    
    .confidence-bar {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        height: 10px;
        overflow: hidden;
        margin: 10px 0;
    }
    
    .confidence-fill {
        background: white;
        height: 100%;
        border-radius: 10px;
        transition: width 0.5s ease;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }
    
    .tips-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .camera-controls {
        text-align: center;
        margin: 20px 0;
    }
    
    .camera-controls button {
        margin: 0 10px;
        border-radius: 25px;
        padding: 10px 20px;
    }
    
    .status-indicator {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-ready {
        background: #28a745;
        color: white;
    }
    
    .status-analyzing {
        background: #ffc107;
        color: black;
    }
    
    .status-error {
        background: #dc3545;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="text-center mb-4">
                <h2 class="text-white">
                    <i class="fas fa-camera"></i> Analyse par Caméra
                </h2>
                <p class="text-white-50">
                    Positionnez votre visage dans le cadre et cliquez pour analyser votre type de peau
                </p>
            </div>
            
            <!-- Camera Section -->
            <div class="row">
                <div class="col-md-8">
                    <div class="camera-container">
                        <video id="videoElement" autoplay muted playsinline></video>
                        <canvas id="canvasElement" style="display: none;"></canvas>
                        
                        <div class="camera-overlay">
                            <div class="face-guide"></div>
                            <button class="capture-btn" id="captureBtn" title="Capturer et analyser">
                                <i class="fas fa-camera"></i>
                            </button>
                            <div class="status-indicator status-ready" id="statusIndicator">
                                Prêt
                            </div>
                        </div>
                    </div>
                    
                    <div class="camera-controls">
                        <button class="btn btn-outline-light" id="startCameraBtn">
                            <i class="fas fa-play"></i> Démarrer la caméra
                        </button>
                        <button class="btn btn-outline-light" id="stopCameraBtn" style="display: none;">
                            <i class="fas fa-stop"></i> Arrêter la caméra
                        </button>
                        <button class="btn btn-outline-light" id="switchCameraBtn" style="display: none;">
                            <i class="fas fa-sync-alt"></i> Changer de caméra
                        </button>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="tips-card">
                        <h5><i class="fas fa-lightbulb text-warning"></i> Conseils pour une meilleure analyse</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Éclairage naturel de préférence</li>
                            <li><i class="fas fa-check text-success"></i> Visage propre, sans maquillage</li>
                            <li><i class="fas fa-check text-success"></i> Regardez directement la caméra</li>
                            <li><i class="fas fa-check text-success"></i> Restez immobile pendant l'analyse</li>
                            <li><i class="fas fa-check text-success"></i> Distance d'environ 50cm</li>
                        </ul>
                    </div>
                    
                    <div class="tips-card">
                        <h6><i class="fas fa-info-circle text-info"></i> Comment ça marche ?</h6>
                        <p class="small">
                            Notre IA analyse la texture, la couleur et les caractéristiques de votre peau 
                            pour déterminer votre type de peau avec précision.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Loading -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">Analyse en cours...</span>
                </div>
                <p class="text-white mt-2">Analyse de votre peau en cours...</p>
            </div>
            
            <!-- Results -->
            <div id="analysisResults" style="display: none;">
                <div class="analysis-result">
                    <h3><i class="fas fa-microscope"></i> Résultats de l'Analyse</h3>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5>Type de peau détecté</h5>
                            <h2 id="detectedSkinType">-</h2>
                        </div>
                        <div class="col-md-6">
                            <h5>Niveau de confiance</h5>
                            <div class="confidence-bar">
                                <div class="confidence-fill" id="confidenceFill" style="width: 0%"></div>
                            </div>
                            <span id="confidenceText">0%</span>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button class="btn btn-light btn-lg" id="getRecommendationsBtn">
                            <i class="fas fa-shopping-bag"></i> Voir les Recommandations
                        </button>
                        <button class="btn btn-outline-light btn-lg ms-2" id="retakePhotoBtn">
                            <i class="fas fa-redo"></i> Reprendre une Photo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const videoElement = document.getElementById('videoElement');
    const canvasElement = document.getElementById('canvasElement');
    const captureBtn = document.getElementById('captureBtn');
    const startCameraBtn = document.getElementById('startCameraBtn');
    const stopCameraBtn = document.getElementById('stopCameraBtn');
    const switchCameraBtn = document.getElementById('switchCameraBtn');
    const statusIndicator = document.getElementById('statusIndicator');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const analysisResults = document.getElementById('analysisResults');
    const getRecommendationsBtn = document.getElementById('getRecommendationsBtn');
    const retakePhotoBtn = document.getElementById('retakePhotoBtn');
    
    let currentStream = null;
    let currentDeviceId = null;
    let availableCameras = [];
    let currentCameraIndex = 0;
    
    // Initialize camera on page load
    initializeCamera();
    
    async function initializeCamera() {
        try {
            // Get available cameras
            const devices = await navigator.mediaDevices.enumerateDevices();
            availableCameras = devices.filter(device => device.kind === 'videoinput');
            
            if (availableCameras.length > 1) {
                switchCameraBtn.style.display = 'inline-block';
            }
            
            await startCamera();
        } catch (error) {
            console.error('Error initializing camera:', error);
            updateStatus('Erreur caméra', 'error');
        }
    }
    
    async function startCamera() {
        try {
            const constraints = {
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                }
            };
            
            if (currentDeviceId) {
                constraints.video.deviceId = { exact: currentDeviceId };
            }
            
            currentStream = await navigator.mediaDevices.getUserMedia(constraints);
            videoElement.srcObject = currentStream;
            
            startCameraBtn.style.display = 'none';
            stopCameraBtn.style.display = 'inline-block';
            captureBtn.style.display = 'block';
            
            updateStatus('Prêt', 'ready');
            
        } catch (error) {
            console.error('Error starting camera:', error);
            updateStatus('Erreur caméra', 'error');
            alert('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
        }
    }
    
    function stopCamera() {
        if (currentStream) {
            currentStream.getTracks().forEach(track => track.stop());
            currentStream = null;
        }
        
        videoElement.srcObject = null;
        startCameraBtn.style.display = 'inline-block';
        stopCameraBtn.style.display = 'none';
        captureBtn.style.display = 'none';
        
        updateStatus('Arrêtée', 'error');
    }
    
    async function switchCamera() {
        if (availableCameras.length <= 1) return;
        
        currentCameraIndex = (currentCameraIndex + 1) % availableCameras.length;
        currentDeviceId = availableCameras[currentCameraIndex].deviceId;
        
        stopCamera();
        await startCamera();
    }
    
    function updateStatus(text, type) {
        statusIndicator.textContent = text;
        statusIndicator.className = `status-indicator status-${type}`;
    }
    
    async function captureAndAnalyze() {
        if (!currentStream) {
            alert('Veuillez démarrer la caméra d\'abord.');
            return;
        }
        
        updateStatus('Analyse...', 'analyzing');
        loadingSpinner.style.display = 'block';
        analysisResults.style.display = 'none';
        
        // Capture image from video
        const context = canvasElement.getContext('2d');
        canvasElement.width = videoElement.videoWidth;
        canvasElement.height = videoElement.videoHeight;
        context.drawImage(videoElement, 0, 0);
        
        // Convert to blob
        canvasElement.toBlob(async (blob) => {
            try {
                const formData = new FormData();
                formData.append('image', blob, 'capture.jpg');
                
                const response = await fetch('{% url "skin_analysis:analyze_image_api" %}', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    displayResults(result);
                } else {
                    throw new Error(result.error || 'Erreur d\'analyse');
                }
                
            } catch (error) {
                console.error('Analysis error:', error);
                alert('Erreur lors de l\'analyse: ' + error.message);
                updateStatus('Erreur', 'error');
            } finally {
                loadingSpinner.style.display = 'none';
            }
        }, 'image/jpeg', 0.8);
    }
    
    function displayResults(result) {
        const skinTypeNames = {
            'oily': 'Peau Grasse',
            'dry': 'Peau Sèche',
            'combination': 'Peau Mixte',
            'sensitive': 'Peau Sensible',
            'normal': 'Peau Normale',
            'acne_prone': 'Peau à Tendance Acnéique'
        };
        
        document.getElementById('detectedSkinType').textContent = 
            skinTypeNames[result.skin_type] || result.skin_type;
        
        const confidence = Math.round(result.confidence * 100);
        document.getElementById('confidenceText').textContent = confidence + '%';
        document.getElementById('confidenceFill').style.width = confidence + '%';
        
        analysisResults.style.display = 'block';
        updateStatus('Analysé', 'ready');
        
        // Store result for recommendations
        analysisResults.dataset.skinType = result.skin_type;
    }
    
    // Event listeners
    startCameraBtn.addEventListener('click', startCamera);
    stopCameraBtn.addEventListener('click', stopCamera);
    switchCameraBtn.addEventListener('click', switchCamera);
    captureBtn.addEventListener('click', captureAndAnalyze);
    
    retakePhotoBtn.addEventListener('click', function() {
        analysisResults.style.display = 'none';
        updateStatus('Prêt', 'ready');
    });
    
    getRecommendationsBtn.addEventListener('click', function() {
        const skinType = analysisResults.dataset.skinType;
        if (skinType) {
            window.location.href = `/products/recommendations/${skinType}/`;
        }
    });
});
</script>
{% endblock %}

from django.db import models
from skin_analysis.models import SkinType

class Brand(models.Model):
    """Marques de produits cosmétiques"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    country = models.CharField(max_length=50, default='Tunisie')
    website = models.URLField(blank=True)
    logo = models.ImageField(upload_to='brands/', null=True, blank=True)
    is_tunisian = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class ProductCategory(models.Model):
    """Catégories de produits"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Product Categories"

    def __str__(self):
        return self.name

class Product(models.Model):
    """Produits cosmétiques"""
    PRODUCT_TYPES = [
        ('cleanser', 'Nettoyant'),
        ('toner', 'Tonique'),
        ('serum', 'Sérum'),
        ('moisturizer', 'Hydratant'),
        ('sunscreen', 'Protection solaire'),
        ('treatment', 'Traitement'),
        ('mask', 'Masque'),
        ('exfoliant', 'Exfoliant'),
        ('oil', 'Huile'),
        ('other', 'Autre'),
    ]

    name = models.CharField(max_length=200)
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE)
    category = models.ForeignKey(ProductCategory, on_delete=models.CASCADE)
    product_type = models.CharField(max_length=20, choices=PRODUCT_TYPES)
    description = models.TextField()
    ingredients = models.TextField()
    suitable_skin_types = models.ManyToManyField(SkinType)
    image = models.ImageField(upload_to='products/', null=True, blank=True)
    volume = models.CharField(max_length=50, blank=True)  # ex: "50ml", "100g"
    usage_instructions = models.TextField(blank=True)
    benefits = models.JSONField(default=list)  # Liste des bénéfices
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.brand.name} - {self.name}"

    def get_current_price(self):
        """Retourne le prix actuel du produit"""
        latest_price = self.prices.filter(is_active=True).order_by('-date_recorded').first()
        return latest_price.price if latest_price else None

class ProductRecommendation(models.Model):
    """Recommandations de produits basées sur le type de peau"""
    skin_type = models.ForeignKey(SkinType, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    priority = models.PositiveIntegerField(default=1)  # 1 = haute priorité
    reason = models.TextField()  # Pourquoi ce produit est recommandé
    created_by = models.CharField(max_length=100, default='system')  # qui a créé la recommandation
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['skin_type', 'product']
        ordering = ['priority', '-created_at']

    def __str__(self):
        return f"{self.skin_type} -> {self.product.name}"

{% extends 'skin_analysis/base.html' %}

{% block title %}Mon Profil - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row">
        <!-- Profile Info -->
        <div class="col-lg-4">
            <div class="card" style="border-radius: 15px;">
                <div class="card-header bg-primary text-white text-center">
                    <h4><i class="fas fa-user-circle"></i> Mon Profil</h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-circle fa-5x text-primary"></i>
                    </div>
                    <h5>{{ user.username }}</h5>
                    {% if user.email %}
                    <p class="text-muted">{{ user.email }}</p>
                    {% endif %}
                    
                    {% if profile.skin_type %}
                    <div class="mt-3">
                        <span class="badge bg-success p-2">
                            <i class="fas fa-spa"></i> {{ profile.skin_type.get_name_display }}
                        </span>
                    </div>
                    {% endif %}
                    
                    {% if profile.age %}
                    <p class="mt-2"><i class="fas fa-birthday-cake"></i> {{ profile.age }} ans</p>
                    {% endif %}
                    
                    <small class="text-muted">
                        Membre depuis {{ user.date_joined|date:"F Y" }}
                    </small>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-3" style="border-radius: 15px;">
                <div class="card-body">
                    <h6><i class="fas fa-bolt"></i> Actions Rapides</h6>
                    <div class="d-grid gap-2">
                        <a href="{% url 'skin_analysis:form_analysis' %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-search"></i> Nouvelle Analyse
                        </a>
                        <a href="{% url 'skin_analysis:camera_analysis' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-camera"></i> Analyse Caméra
                        </a>
                        {% if profile.skin_type %}
                        <a href="{% url 'products:recommendations' profile.skin_type.name %}" class="btn btn-success btn-sm">
                            <i class="fas fa-shopping-bag"></i> Mes Recommandations
                        </a>
                        {% endif %}
                        <a href="{% url 'price_tracking:alerts' %}" class="btn btn-warning btn-sm">
                            <i class="fas fa-bell"></i> Alertes Prix
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Profile Form -->
            <div class="card mb-4" style="border-radius: 15px;">
                <div class="card-header">
                    <h5><i class="fas fa-edit"></i> Informations Personnelles</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.age.id_for_label }}" class="form-label">
                                    <i class="fas fa-birthday-cake"></i> Âge
                                </label>
                                {{ form.age }}
                                {% if form.age.errors %}
                                    <div class="text-danger small">{{ form.age.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.skin_concerns.id_for_label }}" class="form-label">
                                <i class="fas fa-exclamation-triangle"></i> Préoccupations Cutanées
                            </label>
                            {{ form.skin_concerns }}
                            {% if form.skin_concerns.errors %}
                                <div class="text-danger small">{{ form.skin_concerns.errors }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Décrivez vos principales préoccupations (acné, rides, taches, sensibilité, etc.)
                            </small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.allergies.id_for_label }}" class="form-label">
                                <i class="fas fa-shield-alt"></i> Allergies Connues
                            </label>
                            {{ form.allergies }}
                            {% if form.allergies.errors %}
                                <div class="text-danger small">{{ form.allergies.errors }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Mentionnez vos allergies aux ingrédients cosmétiques
                            </small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Sauvegarder
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Recent Analyses -->
            <div class="card" style="border-radius: 15px;">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> Mes Analyses Récentes</h5>
                </div>
                <div class="card-body">
                    {% if recent_analyses %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Méthode</th>
                                    <th>Type Détecté</th>
                                    <th>Confiance</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for analysis in recent_analyses %}
                                <tr>
                                    <td>{{ analysis.created_at|date:"d/m/Y H:i" }}</td>
                                    <td>
                                        {% if analysis.method == 'form' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-clipboard-list"></i> Formulaire
                                            </span>
                                        {% elif analysis.method == 'camera' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-camera"></i> Caméra
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-upload"></i> Image
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if analysis.detected_skin_type %}
                                            <strong>{{ analysis.detected_skin_type.get_name_display }}</strong>
                                        {% else %}
                                            <span class="text-muted">Non déterminé</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if analysis.confidence_score %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ analysis.confidence_score|floatformat:0 }}%">
                                                    {{ analysis.confidence_score|floatformat:0 }}%
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'skin_analysis:results' analysis.id %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> Voir
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h6>Aucune analyse effectuée</h6>
                        <p class="text-muted">Commencez par analyser votre type de peau</p>
                        <a href="{% url 'skin_analysis:form_analysis' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Première Analyse
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 10px;
}

.progress {
    border-radius: 10px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}
</style>
{% endblock %}

from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from .models import Product, Brand, ProductRecommendation
from skin_analysis.models import SkinType

def product_list(request):
    """Liste des produits avec filtres"""
    products = Product.objects.filter(is_active=True).select_related('brand', 'category')

    # Filtres
    brand_filter = request.GET.get('brand')
    category_filter = request.GET.get('category')
    skin_type_filter = request.GET.get('skin_type')

    if brand_filter:
        products = products.filter(brand_id=brand_filter)
    if category_filter:
        products = products.filter(category_id=category_filter)
    if skin_type_filter:
        products = products.filter(suitable_skin_types__name=skin_type_filter)

    # Pagination
    paginator = Paginator(products, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Données pour les filtres
    brands = Brand.objects.filter(is_active=True)
    skin_types = SkinType.objects.all()

    context = {
        'page_obj': page_obj,
        'brands': brands,
        'skin_types': skin_types,
        'current_filters': {
            'brand': brand_filter,
            'category': category_filter,
            'skin_type': skin_type_filter,
        }
    }
    return render(request, 'products/list.html', context)

def product_detail(request, product_id):
    """Détail d'un produit"""
    product = get_object_or_404(Product, id=product_id, is_active=True)

    # Produits similaires
    similar_products = Product.objects.filter(
        category=product.category,
        is_active=True
    ).exclude(id=product.id)[:4]

    context = {
        'product': product,
        'similar_products': similar_products,
    }
    return render(request, 'products/detail.html', context)

def brand_list(request):
    """Liste des marques"""
    brands = Brand.objects.filter(is_active=True)
    return render(request, 'products/brands.html', {'brands': brands})

def brand_detail(request, brand_id):
    """Détail d'une marque"""
    brand = get_object_or_404(Brand, id=brand_id, is_active=True)
    products = Product.objects.filter(brand=brand, is_active=True)

    context = {
        'brand': brand,
        'products': products,
    }
    return render(request, 'products/brand_detail.html', context)

def recommendations_by_skin_type(request, skin_type):
    """Recommandations par type de peau"""
    try:
        skin_type_obj = SkinType.objects.get(name=skin_type)
        recommendations = ProductRecommendation.objects.filter(
            skin_type=skin_type_obj
        ).select_related('product', 'product__brand')

        context = {
            'skin_type': skin_type_obj,
            'recommendations': recommendations,
        }
        return render(request, 'products/recommendations.html', context)
    except SkinType.DoesNotExist:
        return render(request, 'products/recommendations.html', {'error': 'Type de peau non trouvé'})

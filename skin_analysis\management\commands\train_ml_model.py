"""
Commande Django pour entraîner le modèle ML de classification de peau
Usage: python manage.py train_ml_model
"""

import os
import json
import numpy as np
from django.core.management.base import BaseCommand
from django.conf import settings
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from skin_analysis.ml_models import SkinTypeClassifier
from skin_analysis.data_collection import DataCollector

class Command(BaseCommand):
    help = 'Entraîne le modèle ML pour la classification de type de peau'

    def add_arguments(self, parser):
        parser.add_argument(
            '--epochs',
            type=int,
            default=50,
            help='Nombre d\'époques d\'entraînement'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=32,
            help='Taille des batches'
        )
        parser.add_argument(
            '--validation-split',
            type=float,
            default=0.2,
            help='Proportion des données pour la validation'
        )
        parser.add_argument(
            '--use-augmentation',
            action='store_true',
            help='Utiliser l\'augmentation de données'
        )
        parser.add_argument(
            '--model-type',
            choices=['base', 'advanced'],
            default='advanced',
            help='Type de modèle à entraîner'
        )
        parser.add_argument(
            '--dataset-path',
            type=str,
            help='Chemin vers un dataset spécifique (ex: real_datasets/synthetic_demo)'
        )
        parser.add_argument(
            '--use-real-dataset',
            action='store_true',
            help='Utiliser un vrai dataset au lieu des données collectées'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Début de l\'entraînement du modèle ML'))
        
        # Initialiser les composants
        classifier = SkinTypeClassifier()
        data_collector = DataCollector()
        
        # Vérifier les données selon la source
        if options['use_real_dataset'] or options['dataset_path']:
            # Pour les vrais datasets, on vérifiera après le chargement
            self.stdout.write("📊 Utilisation d'un dataset réel")
        else:
            # Vérifier les données collectées
            stats = data_collector.get_dataset_stats()
            self.stdout.write(f"📊 Dataset collecté: {stats['total']} échantillons")

            if stats['total'] < 100:
                self.stdout.write(
                    self.style.WARNING(
                        '⚠️  Dataset trop petit (< 100 échantillons). '
                        'Utilisez --use-real-dataset ou collectez plus de données.'
                    )
                )
                return
        
        # Afficher la répartition par type (seulement pour les données collectées)
        if not (options['use_real_dataset'] or options['dataset_path']):
            for skin_type, count in stats['by_type'].items():
                self.stdout.write(f"  - {skin_type}: {count} échantillons")
        
        try:
            # Charger les données selon la source
            if options['use_real_dataset'] or options['dataset_path']:
                dataset_path = options['dataset_path']
                if not dataset_path:
                    # Utiliser le dataset synthétique par défaut
                    from skin_analysis.dataset_downloader import dataset_downloader
                    dataset_path = os.path.join(dataset_downloader.base_path, 'synthetic_demo')

                X, y, class_names = self.load_real_dataset(dataset_path)
                self.stdout.write(f"📊 Utilisation du dataset: {dataset_path}")
            else:
                X, y, class_names = self.load_dataset(data_collector)
                self.stdout.write("📊 Utilisation des données collectées")

            if len(X) == 0:
                self.stdout.write(self.style.ERROR('❌ Aucune donnée valide trouvée'))
                self.stdout.write(self.style.WARNING('💡 Essayez: python manage.py download_datasets --dataset synthetic_demo'))
                return
            
            # Diviser les données
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, 
                test_size=options['validation_split'],
                stratify=y,
                random_state=42
            )
            
            self.stdout.write(f"📈 Entraînement: {len(X_train)} échantillons")
            self.stdout.write(f"📊 Validation: {len(X_val)} échantillons")
            
            # Créer le modèle
            if options['model_type'] == 'advanced':
                model = classifier.create_advanced_model()
                self.stdout.write("🧠 Modèle avancé EfficientNetB3 créé")
            else:
                classifier.create_base_model()
                model = classifier.model
                self.stdout.write("🧠 Modèle de base EfficientNetB0 créé")
            
            # Préparer les générateurs de données
            if options['use_augmentation']:
                train_gen = self.create_augmented_generator()
                self.stdout.write("🔄 Augmentation de données activée")
            else:
                train_gen = None
            
            # Callbacks pour l'entraînement
            callbacks = self.create_callbacks()
            
            # Entraînement
            self.stdout.write("🏋️ Début de l'entraînement...")
            
            if train_gen:
                # Avec augmentation
                history = model.fit(
                    train_gen.flow(X_train, y_train, batch_size=options['batch_size']),
                    steps_per_epoch=len(X_train) // options['batch_size'],
                    epochs=options['epochs'],
                    validation_data=(X_val, y_val),
                    callbacks=callbacks,
                    verbose=1
                )
            else:
                # Sans augmentation
                history = model.fit(
                    X_train, y_train,
                    batch_size=options['batch_size'],
                    epochs=options['epochs'],
                    validation_data=(X_val, y_val),
                    callbacks=callbacks,
                    verbose=1
                )
            
            # Évaluation
            self.stdout.write("📊 Évaluation du modèle...")
            val_loss, val_accuracy = model.evaluate(X_val, y_val, verbose=0)
            
            self.stdout.write(f"✅ Précision finale: {val_accuracy:.4f}")
            self.stdout.write(f"📉 Perte finale: {val_loss:.4f}")
            
            # Prédictions détaillées
            y_pred = model.predict(X_val)
            y_pred_classes = np.argmax(y_pred, axis=1)
            y_true_classes = np.argmax(y_val, axis=1)
            
            # Rapport de classification
            report = classification_report(
                y_true_classes, y_pred_classes,
                target_names=class_names,
                output_dict=True
            )
            
            self.stdout.write("\n📋 Rapport de classification:")
            for class_name in class_names:
                metrics = report[class_name]
                self.stdout.write(
                    f"  {class_name}: "
                    f"Précision={metrics['precision']:.3f}, "
                    f"Rappel={metrics['recall']:.3f}, "
                    f"F1={metrics['f1-score']:.3f}"
                )
            
            # Sauvegarder le modèle
            classifier.model = model
            classifier.save_model()
            
            self.stdout.write(self.style.SUCCESS('🎉 Modèle entraîné et sauvegardé avec succès!'))
            
            # Sauvegarder les métriques
            self.save_training_metrics(history, report, options)
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Erreur lors de l\'entraînement: {e}'))
            import traceback
            self.stdout.write(traceback.format_exc())

    def load_dataset(self, data_collector):
        """Charge le dataset depuis les annotations"""
        try:
            with open(data_collector.annotations_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)
        except FileNotFoundError:
            self.stdout.write(self.style.ERROR('❌ Fichier d\'annotations non trouvé'))
            return [], [], []
        
        X = []
        y = []
        class_names = ['normal', 'oily', 'dry', 'combination', 'sensitive', 'acne_prone']
        
        for annotation in annotations:
            try:
                # Charger l'image
                image_path = os.path.join(data_collector.dataset_path, annotation['filename'])
                
                if not os.path.exists(image_path):
                    continue
                
                # Préprocesser l'image
                img = tf.keras.preprocessing.image.load_img(
                    image_path, target_size=(224, 224)
                )
                img_array = tf.keras.preprocessing.image.img_to_array(img)
                img_array = img_array / 255.0  # Normalisation
                
                # Label one-hot
                skin_type = annotation['skin_type']
                if skin_type in class_names:
                    label = tf.keras.utils.to_categorical(
                        class_names.index(skin_type), 
                        len(class_names)
                    )
                    
                    X.append(img_array)
                    y.append(label)
                
            except Exception as e:
                self.stdout.write(f"⚠️  Erreur chargement {annotation.get('filename', 'unknown')}: {e}")
                continue
        
        return np.array(X), np.array(y), class_names

    def load_real_dataset(self, dataset_path):
        """Charge un vrai dataset depuis le chemin spécifié"""
        annotations_file = os.path.join(dataset_path, 'annotations.json')

        if not os.path.exists(annotations_file):
            self.stdout.write(self.style.ERROR(f'❌ Fichier d\'annotations non trouvé: {annotations_file}'))
            return [], [], []

        try:
            with open(annotations_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Erreur lecture annotations: {e}'))
            return [], [], []

        X = []
        y = []
        class_names = ['normal', 'oily', 'dry', 'combination', 'sensitive', 'acne_prone']

        self.stdout.write(f"📁 Chargement depuis: {dataset_path}")
        self.stdout.write(f"📋 Annotations trouvées: {len(annotations)}")

        loaded_count = 0
        error_count = 0

        for annotation in annotations:
            try:
                # Construire le chemin de l'image
                if 'filename' in annotation:
                    image_path = os.path.join(dataset_path, annotation['filename'])
                else:
                    # Fallback pour différents formats d'annotations
                    skin_type = annotation['skin_type']
                    filename = f"{skin_type}/{annotation.get('id', 'unknown')}.jpg"
                    image_path = os.path.join(dataset_path, filename)

                if not os.path.exists(image_path):
                    error_count += 1
                    continue

                # Charger et préprocesser l'image
                img = tf.keras.preprocessing.image.load_img(
                    image_path, target_size=(224, 224)
                )
                img_array = tf.keras.preprocessing.image.img_to_array(img)
                img_array = img_array / 255.0  # Normalisation

                # Label one-hot
                skin_type = annotation['skin_type']
                if skin_type in class_names:
                    label = tf.keras.utils.to_categorical(
                        class_names.index(skin_type),
                        len(class_names)
                    )

                    X.append(img_array)
                    y.append(label)
                    loaded_count += 1

                    if loaded_count % 100 == 0:
                        self.stdout.write(f"📈 Chargé: {loaded_count} images...")

            except Exception as e:
                error_count += 1
                if error_count <= 5:  # Afficher seulement les 5 premières erreurs
                    self.stdout.write(f"⚠️  Erreur chargement {annotation.get('filename', 'unknown')}: {e}")

        self.stdout.write(f"✅ Images chargées: {loaded_count}")
        if error_count > 0:
            self.stdout.write(f"⚠️  Erreurs: {error_count}")

        return np.array(X), np.array(y), class_names

    def create_augmented_generator(self):
        """Crée un générateur avec augmentation de données"""
        return ImageDataGenerator(
            rotation_range=10,
            width_shift_range=0.1,
            height_shift_range=0.1,
            shear_range=0.1,
            zoom_range=0.1,
            horizontal_flip=True,
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )

    def create_callbacks(self):
        """Crée les callbacks pour l'entraînement"""
        callbacks = []
        
        # Early stopping
        early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=10,
            restore_best_weights=True,
            verbose=1
        )
        callbacks.append(early_stopping)
        
        # Réduction du learning rate
        reduce_lr = keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.2,
            patience=5,
            min_lr=0.0001,
            verbose=1
        )
        callbacks.append(reduce_lr)
        
        # Sauvegarde du meilleur modèle
        checkpoint_path = os.path.join(settings.BASE_DIR, 'ml_models', 'best_model.h5')
        os.makedirs(os.path.dirname(checkpoint_path), exist_ok=True)
        
        checkpoint = keras.callbacks.ModelCheckpoint(
            checkpoint_path,
            monitor='val_accuracy',
            save_best_only=True,
            verbose=1
        )
        callbacks.append(checkpoint)
        
        return callbacks

    def save_training_metrics(self, history, classification_report, options):
        """Sauvegarde les métriques d'entraînement"""
        metrics = {
            'training_options': options,
            'final_metrics': {
                'val_accuracy': float(max(history.history['val_accuracy'])),
                'val_loss': float(min(history.history['val_loss'])),
                'train_accuracy': float(max(history.history['accuracy'])),
                'train_loss': float(min(history.history['loss']))
            },
            'classification_report': classification_report,
            'training_history': {
                'accuracy': [float(x) for x in history.history['accuracy']],
                'val_accuracy': [float(x) for x in history.history['val_accuracy']],
                'loss': [float(x) for x in history.history['loss']],
                'val_loss': [float(x) for x in history.history['val_loss']]
            }
        }
        
        metrics_path = os.path.join(settings.BASE_DIR, 'ml_models', 'training_metrics.json')
        os.makedirs(os.path.dirname(metrics_path), exist_ok=True)
        
        with open(metrics_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)
        
        self.stdout.write(f"📊 Métriques sauvegardées: {metrics_path}")

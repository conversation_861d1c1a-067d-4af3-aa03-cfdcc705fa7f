from django.db import models
from django.contrib.auth.models import User
from PIL import Image
import os

class SkinType(models.Model):
    """Types de peau possibles"""
    SKIN_TYPES = [
        ('oily', 'Peau Grasse'),
        ('dry', 'Peau Sèche'),
        ('combination', 'Peau Mixte'),
        ('sensitive', 'Peau Sensible'),
        ('normal', 'Peau Normale'),
        ('acne_prone', 'Peau à Tendance Acnéique'),
    ]

    name = models.CharField(max_length=50, choices=SKIN_TYPES, unique=True)
    description = models.TextField()
    characteristics = models.JSONField(default=dict)  # Stocke les caractéristiques spécifiques
    care_tips = models.TextField()

    def __str__(self):
        return self.get_name_display()

class UserProfile(models.Model):
    """Profil utilisateur étendu"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    age = models.PositiveIntegerField(null=True, blank=True)
    skin_type = models.ForeignKey(SkinType, on_delete=models.SET_NULL, null=True, blank=True)
    skin_concerns = models.JSONField(default=list)  # Liste des préoccupations
    allergies = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Profil de {self.user.username}"

class SkinAnalysis(models.Model):
    """Analyse de peau effectuée"""
    ANALYSIS_METHODS = [
        ('form', 'Formulaire'),
        ('camera', 'Caméra'),
        ('upload', 'Upload d\'image'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    method = models.CharField(max_length=20, choices=ANALYSIS_METHODS)
    image = models.ImageField(upload_to='skin_analysis/', null=True, blank=True)
    detected_skin_type = models.ForeignKey(SkinType, on_delete=models.SET_NULL, null=True)
    confidence_score = models.FloatField(null=True, blank=True)  # Score de confiance de l'IA
    analysis_data = models.JSONField(default=dict)  # Données détaillées de l'analyse
    form_responses = models.JSONField(default=dict)  # Réponses du formulaire si applicable
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Analyse {self.method} - {self.user.username} - {self.created_at.strftime('%d/%m/%Y')}"

class SkinAnalysisQuestion(models.Model):
    """Questions pour l'analyse par formulaire"""
    QUESTION_TYPES = [
        ('single_choice', 'Choix unique'),
        ('multiple_choice', 'Choix multiple'),
        ('scale', 'Échelle'),
        ('text', 'Texte libre'),
    ]

    question_text = models.TextField()
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES)
    choices = models.JSONField(default=list)  # Options pour les questions à choix
    weight = models.FloatField(default=1.0)  # Poids de la question dans l'analyse
    skin_type_mapping = models.JSONField(default=dict)  # Mapping réponse -> type de peau
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"Q{self.order}: {self.question_text[:50]}..."

"""
Commande de debug pour l'analyseur dermatologique
"""

import os
from django.core.management.base import BaseCommand
from skin_analysis.dermatology_analyzer import professional_analyzer

class Command(BaseCommand):
    help = 'Debug de l\'analyseur dermatologique professionnel'

    def add_arguments(self, parser):
        parser.add_argument(
            '--image-path',
            type=str,
            required=True,
            help='Chemin vers l\'image à analyser'
        )

    def handle(self, *args, **options):
        image_path = options['image_path']
        
        if not os.path.exists(image_path):
            self.stdout.write(self.style.ERROR(f'❌ Image non trouvée: {image_path}'))
            return
        
        self.stdout.write(self.style.SUCCESS('🔬 Debug Analyseur Dermatologique'))
        self.stdout.write(f'📁 Image: {image_path}')
        
        try:
            # Test direct de l'analyseur
            result = professional_analyzer.analyze_professional(image_path)
            
            if result:
                self.stdout.write(self.style.SUCCESS('✅ Analyse réussie'))
                self.stdout.write(f'🎯 Type détecté: {result["skin_type"]}')
                self.stdout.write(f'📊 Confiance: {result["confidence"]:.1%}')
                self.stdout.write(f'📈 Pourcentages: {result["percentages"]}')
                
                # Détails des analyses
                details = result['detailed_analysis']
                self.stdout.write('\n🔍 Analyses détaillées:')
                
                sebum = details['sebum_analysis']
                self.stdout.write(f'  💧 Sébum: ratio={sebum["sebum_ratio"]:.3f}, distribution={sebum["sebum_distribution"]:.3f}')
                
                pore = details['pore_analysis']
                self.stdout.write(f'  🕳️  Pores: visibilité={pore["pore_visibility"]:.3f}, densité={pore["pore_density"]:.3f}')
                
                texture = details['texture_analysis']
                self.stdout.write(f'  🌊 Texture: rugosité={texture["texture_roughness"]:.3f}, variance={texture["laplacian_variance"]:.1f}')
                
                inflammation = details['inflammation_analysis']
                self.stdout.write(f'  🔴 Inflammation: score={inflammation["inflammation_score"]:.3f}, lésions={inflammation["lesion_count"]}')
                
            else:
                self.stdout.write(self.style.ERROR('❌ Analyse échouée'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Erreur: {e}'))
            import traceback
            traceback.print_exc()

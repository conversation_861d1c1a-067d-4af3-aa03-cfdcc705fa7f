{% extends 'skin_analysis/base.html' %}

{% block title %}Utiliser votre téléphone comme caméra - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="text-white">
                <i class="fas fa-mobile-alt"></i> Utiliser votre Téléphone comme Caméra
            </h2>
            <p class="text-white-50">Guide complet pour connecter votre téléphone à votre PC</p>
        </div>
    </div>
    
    <div class="row">
        <!-- Android Guide -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-header bg-success text-white">
                    <h4><i class="fab fa-android"></i> Android</h4>
                </div>
                <div class="card-body">
                    <h5>📱 Méthode 1 : DroidCam (Recommandée)</h5>
                    
                    <div class="accordion" id="androidAccordion">
                        <div class="accordion-item">
                            <h6 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#android-step1">
                                    Étape 1 : Préparation du téléphone
                                </button>
                            </h6>
                            <div id="android-step1" class="accordion-collapse collapse show" data-bs-parent="#androidAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>Allez dans <strong>Paramètres</strong> → <strong>À propos du téléphone</strong></li>
                                        <li>Appuyez <strong>7 fois</strong> sur "Numéro de build"</li>
                                        <li>Retournez dans <strong>Paramètres</strong> → <strong>Options développeur</strong></li>
                                        <li>Activez <strong>"Débogage USB"</strong></li>
                                        <li>Téléchargez <strong>"DroidCam"</strong> depuis Google Play Store</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h6 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#android-step2">
                                    Étape 2 : Installation PC
                                </button>
                            </h6>
                            <div id="android-step2" class="accordion-collapse collapse" data-bs-parent="#androidAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>Allez sur <a href="https://www.dev47apps.com/" target="_blank">dev47apps.com</a></li>
                                        <li>Téléchargez <strong>"DroidCam Client"</strong> pour Windows</li>
                                        <li>Installez et lancez l'application</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h6 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#android-step3">
                                    Étape 3 : Connexion
                                </button>
                            </h6>
                            <div id="android-step3" class="accordion-collapse collapse" data-bs-parent="#androidAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>Connectez votre téléphone au PC via <strong>câble USB</strong></li>
                                        <li>Autorisez le <strong>débogage USB</strong> sur votre téléphone</li>
                                        <li>Lancez <strong>DroidCam</strong> sur les deux appareils</li>
                                        <li>Dans DroidCam PC, sélectionnez <strong>"USB"</strong></li>
                                        <li>Cliquez <strong>"Start"</strong></li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <h6>📶 Alternative WiFi : IP Webcam</h6>
                        <ol>
                            <li>Téléchargez "IP Webcam" sur Google Play</li>
                            <li>Lancez l'app et cliquez "Start server"</li>
                            <li>Notez l'adresse IP (ex: *************:8080)</li>
                            <li>Ouvrez cette adresse dans votre navigateur PC</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- iPhone Guide -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-header bg-primary text-white">
                    <h4><i class="fab fa-apple"></i> iPhone</h4>
                </div>
                <div class="card-body">
                    <h5>📱 Méthode 1 : EpocCam (Recommandée)</h5>
                    
                    <div class="accordion" id="iphoneAccordion">
                        <div class="accordion-item">
                            <h6 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#iphone-step1">
                                    Étape 1 : Installation
                                </button>
                            </h6>
                            <div id="iphone-step1" class="accordion-collapse collapse show" data-bs-parent="#iphoneAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>Téléchargez <strong>"EpocCam"</strong> sur l'App Store</li>
                                        <li>Allez sur <a href="https://www.elgato.com/epoccam" target="_blank">elgato.com/epoccam</a></li>
                                        <li>Téléchargez <strong>"EpocCam Viewer"</strong> pour Windows/Mac</li>
                                        <li>Installez les deux applications</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h6 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#iphone-step2">
                                    Étape 2 : Connexion USB
                                </button>
                            </h6>
                            <div id="iphone-step2" class="accordion-collapse collapse" data-bs-parent="#iphoneAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>Connectez votre iPhone au PC avec le <strong>câble Lightning/USB-C</strong></li>
                                        <li>Lancez <strong>EpocCam</strong> sur votre iPhone</li>
                                        <li>Lancez <strong>EpocCam Viewer</strong> sur votre PC</li>
                                        <li>Autorisez la connexion si demandé</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <h6>🍎 macOS : Continuity Camera</h6>
                        <p>Si vous avez un Mac avec macOS Ventura+ :</p>
                        <ol>
                            <li>Connectez iPhone et Mac au même compte iCloud</li>
                            <li>Activez WiFi et Bluetooth sur les deux</li>
                            <li>Votre iPhone apparaîtra automatiquement</li>
                        </ol>
                    </div>
                    
                    <div class="alert alert-success mt-3">
                        <h6>📱 Alternative : Camo</h6>
                        <ol>
                            <li>Téléchargez "Camo" sur App Store et PC</li>
                            <li>Créez un compte gratuit</li>
                            <li>Connectez via USB ou WiFi</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Troubleshooting -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-header bg-warning text-dark">
                    <h4><i class="fas fa-tools"></i> Dépannage</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>❌ Problèmes Courants</h6>
                            <ul>
                                <li><strong>Téléphone non détecté :</strong> Vérifiez les pilotes USB</li>
                                <li><strong>Image floue :</strong> Nettoyez l'objectif du téléphone</li>
                                <li><strong>Connexion échoue :</strong> Redémarrez les deux applications</li>
                                <li><strong>Pas de son :</strong> Normal, seule la vidéo est transmise</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>✅ Conseils d'Optimisation</h6>
                            <ul>
                                <li><strong>Qualité :</strong> USB > WiFi pour la stabilité</li>
                                <li><strong>Éclairage :</strong> Utilisez la lumière naturelle</li>
                                <li><strong>Position :</strong> Stabilisez votre téléphone</li>
                                <li><strong>Batterie :</strong> Gardez votre téléphone chargé</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="row mt-4 mb-5">
        <div class="col-12 text-center">
            <a href="{% url 'skin_analysis:camera_analysis' %}" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-camera"></i> Tester Maintenant
            </a>
            <a href="{% url 'skin_analysis:form_analysis' %}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-clipboard-list"></i> Utiliser le Formulaire
            </a>
        </div>
    </div>
</div>
{% endblock %}

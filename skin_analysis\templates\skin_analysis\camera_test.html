{% extends 'skin_analysis/base.html' %}
{% block title %}Test Caméra - SkinCare Tunisia{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container mt-5 pt-5">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="text-center mb-4">
        <h2 class="text-white">
          <i class="fas fa-camera"></i> Test Caméra Simple
        </h2>
        <p class="text-white-50">Version simplifiée pour déboguer les problèmes de caméra</p>
      </div>

      <!-- Camera Section -->
      <div class="card">
        <div class="card-body">
          <div class="text-center">
            <video id="videoElement" width="640" height="480" autoplay muted playsinline style="border: 2px solid #ccc; border-radius: 10px;"></video>
            <canvas id="canvasElement" style="display: none;"></canvas>
          </div>
          
          <div class="text-center mt-3">
            <button class="btn btn-primary" id="startBtn">
              <i class="fas fa-play"></i> Démarrer Caméra
            </button>
            <button class="btn btn-danger" id="stopBtn" style="display: none;">
              <i class="fas fa-stop"></i> Arrêter Caméra
            </button>
            <button class="btn btn-success" id="captureBtn" style="display: none;">
              <i class="fas fa-camera"></i> Capturer
            </button>
          </div>
          
          <div class="mt-3">
            <div id="status" class="alert alert-info">
              Prêt à démarrer
            </div>
          </div>
          
          <div id="result" style="display: none;" class="mt-3">
            <div class="alert alert-success">
              <h5>Résultat de l'analyse :</h5>
              <p id="resultText"></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('videoElement');
    const canvas = document.getElementById('canvasElement');
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const captureBtn = document.getElementById('captureBtn');
    const status = document.getElementById('status');
    const result = document.getElementById('result');
    const resultText = document.getElementById('resultText');
    
    let currentStream = null;
    
    function updateStatus(message, type = 'info') {
        status.className = `alert alert-${type}`;
        status.textContent = message;
        console.log('📱 Status:', message);
    }
    
    // Vérifier le support du navigateur
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        updateStatus('❌ Votre navigateur ne supporte pas l\'accès à la caméra', 'danger');
        startBtn.disabled = true;
        return;
    }
    
    // Démarrer la caméra
    startBtn.addEventListener('click', async function() {
        try {
            updateStatus('🔄 Démarrage de la caméra...', 'warning');
            console.log('🚀 Tentative d\'accès à la caméra');
            
            // Demander l'accès à la caméra avec contraintes simples
            currentStream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: false
            });
            
            console.log('✅ Stream obtenu:', currentStream);
            
            // Assigner le stream à la vidéo
            video.srcObject = currentStream;
            
            // Attendre que la vidéo soit prête
            video.onloadedmetadata = function() {
                console.log('📹 Métadonnées chargées');
                video.play().then(() => {
                    console.log('▶️ Lecture démarrée');
                    updateStatus('✅ Caméra active', 'success');
                    
                    // Afficher les boutons
                    startBtn.style.display = 'none';
                    stopBtn.style.display = 'inline-block';
                    captureBtn.style.display = 'inline-block';
                }).catch(e => {
                    console.error('❌ Erreur lecture:', e);
                    updateStatus('❌ Erreur de lecture vidéo', 'danger');
                });
            };
            
        } catch (error) {
            console.error('❌ Erreur caméra:', error);
            let message = 'Erreur: ';
            
            if (error.name === 'NotAllowedError') {
                message += 'Permission refusée. Autorisez l\'accès à la caméra.';
            } else if (error.name === 'NotFoundError') {
                message += 'Aucune caméra trouvée.';
            } else if (error.name === 'NotReadableError') {
                message += 'Caméra utilisée par une autre application.';
            } else {
                message += error.message;
            }
            
            updateStatus(message, 'danger');
        }
    });
    
    // Arrêter la caméra
    stopBtn.addEventListener('click', function() {
        if (currentStream) {
            currentStream.getTracks().forEach(track => track.stop());
            currentStream = null;
        }
        
        video.srcObject = null;
        startBtn.style.display = 'inline-block';
        stopBtn.style.display = 'none';
        captureBtn.style.display = 'none';
        
        updateStatus('⏹️ Caméra arrêtée', 'info');
    });
    
    // Capturer et analyser
    captureBtn.addEventListener('click', async function() {
        if (!currentStream) {
            updateStatus('❌ Aucune caméra active', 'danger');
            return;
        }
        
        try {
            updateStatus('📸 Capture en cours...', 'warning');
            
            // Capturer l'image
            const context = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0);
            
            // Convertir en blob
            canvas.toBlob(async function(blob) {
                if (!blob) {
                    updateStatus('❌ Erreur de capture', 'danger');
                    return;
                }
                
                console.log('📷 Image capturée:', blob.size, 'bytes');
                updateStatus('🔄 Analyse en cours...', 'warning');
                
                // Envoyer pour analyse
                const formData = new FormData();
                formData.append('image', blob, 'capture.jpg');
                
                const response = await fetch('{% url "skin_analysis:analyze_image_api" %}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    }
                });
                
                const data = await response.json();
                console.log('📊 Résultat:', data);
                
                if (data.error) {
                    updateStatus('❌ ' + data.error, 'danger');
                } else {
                    updateStatus('✅ Analyse terminée', 'success');
                    
                    // Afficher le résultat
                    const skinTypes = {
                        'normal': 'Peau Normale',
                        'oily': 'Peau Grasse', 
                        'dry': 'Peau Sèche',
                        'combination': 'Peau Mixte',
                        'sensitive': 'Peau Sensible',
                        'acne_prone': 'Peau à Tendance Acnéique'
                    };
                    
                    const confidence = Math.round(data.confidence * 100);
                    resultText.innerHTML = `
                        <strong>Type détecté:</strong> ${skinTypes[data.skin_type] || data.skin_type}<br>
                        <strong>Confiance:</strong> ${confidence}%
                    `;
                    result.style.display = 'block';
                }
                
            }, 'image/jpeg', 0.9);
            
        } catch (error) {
            console.error('❌ Erreur analyse:', error);
            updateStatus('❌ Erreur d\'analyse: ' + error.message, 'danger');
        }
    });
    
    console.log('🎥 Page caméra test chargée');
});
</script>
{% endblock %}

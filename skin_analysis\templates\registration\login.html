{% extends 'skin_analysis/base.html' %}

{% block title %}Connexion - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card">
                <div class="card-header bg-primary text-white text-center">
                    <h4><i class="fas fa-sign-in-alt"></i> Connexion</h4>
                </div>
                <div class="card-body p-4">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-user"></i> Nom d'utilisateur
                            </label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger small">
                                    {{ form.username.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock"></i> Mot de passe
                            </label>
                            {{ form.password }}
                            {% if form.password.errors %}
                                <div class="text-danger small">
                                    {{ form.password.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i> Se connecter
                            </button>
                        </div>
                        
                        <div class="text-center mt-3">
                            <small>
                                <a href="{% url 'password_reset' %}" class="text-decoration-none">
                                    Mot de passe oublié ?
                                </a>
                            </small>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <small>
                        Pas encore de compte ? 
                        <a href="{% url 'skin_analysis:register' %}" class="text-decoration-none">
                            <strong>Inscrivez-vous</strong>
                        </a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    border-radius: 10px;
    padding: 12px;
    font-weight: 600;
}
</style>
{% endblock %}

{% extends 'skin_analysis/base.html' %}

{% block title %}Marques - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="text-white">
                <i class="fas fa-building"></i> Nos Marques Partenaires
            </h2>
            <p class="text-white-50">Découvrez les marques de confiance disponibles en Tunisie</p>
        </div>
    </div>
    
    <div class="row">
        {% for brand in brands %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 15px; border: none; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                {% if brand.logo %}
                <img src="{{ brand.logo.url }}" class="card-img-top" style="height: 150px; object-fit: contain; padding: 20px;" alt="{{ brand.name }}">
                {% else %}
                <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 150px;">
                    <i class="fas fa-building fa-3x text-muted"></i>
                </div>
                {% endif %}
                
                <div class="card-body text-center">
                    <h5 class="card-title">{{ brand.name }}</h5>
                    <p class="card-text">{{ brand.description }}</p>
                    
                    <div class="mb-3">
                        {% if brand.is_tunisian %}
                        <span class="badge bg-success">
                            <i class="fas fa-flag"></i> Marque Tunisienne
                        </span>
                        {% else %}
                        <span class="badge bg-primary">
                            <i class="fas fa-globe"></i> Disponible en Tunisie
                        </span>
                        {% endif %}
                    </div>
                    
                    <a href="{% url 'products:brand_detail' brand.id %}" class="btn btn-primary">
                        <i class="fas fa-eye"></i> Voir les Produits
                    </a>
                    
                    {% if brand.website %}
                    <a href="{{ brand.website }}" target="_blank" class="btn btn-outline-primary">
                        <i class="fas fa-external-link-alt"></i> Site Web
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

"""
Système de collecte de données pour améliorer le modèle ML
Collecte anonyme d'images avec consentement utilisateur
"""

import os
import uuid
import json
from datetime import datetime
from django.conf import settings
from django.core.files.storage import default_storage
from .models import SkinAnalysis, SkinType
import cv2
import numpy as np

class DataCollector:
    """Collecteur de données pour l'entraînement du modèle"""
    
    def __init__(self):
        self.dataset_path = os.path.join(settings.MEDIA_ROOT, 'ml_dataset')
        self.annotations_file = os.path.join(self.dataset_path, 'annotations.json')
        self.ensure_directories()
    
    def ensure_directories(self):
        """Crée les dossiers nécessaires"""
        skin_types = ['normal', 'oily', 'dry', 'combination', 'sensitive', 'acne_prone']
        
        for skin_type in skin_types:
            os.makedirs(os.path.join(self.dataset_path, skin_type), exist_ok=True)
        
        os.makedirs(os.path.join(self.dataset_path, 'raw'), exist_ok=True)
        os.makedirs(os.path.join(self.dataset_path, 'processed'), exist_ok=True)
    
    def collect_sample(self, image_path, skin_type, user_feedback=None, analysis_data=None, consent=True):
        """
        Collecte un échantillon pour le dataset
        
        Args:
            image_path: Chemin vers l'image
            skin_type: Type de peau confirmé
            user_feedback: Feedback utilisateur sur la prédiction
            analysis_data: Données d'analyse existantes
            consent: Consentement utilisateur pour utiliser les données
        """
        if not consent:
            return False
        
        try:
            # Générer un ID unique
            sample_id = str(uuid.uuid4())
            
            # Copier l'image dans le dataset
            target_dir = os.path.join(self.dataset_path, skin_type)
            target_path = os.path.join(target_dir, f"{sample_id}.jpg")
            
            # Préprocesser et sauvegarder l'image
            processed_image = self.preprocess_for_dataset(image_path)
            cv2.imwrite(target_path, processed_image)
            
            # Créer l'annotation
            annotation = {
                'id': sample_id,
                'filename': f"{skin_type}/{sample_id}.jpg",
                'skin_type': skin_type,
                'timestamp': datetime.now().isoformat(),
                'user_feedback': user_feedback,
                'analysis_data': analysis_data,
                'image_quality': self.assess_image_quality(processed_image),
                'metadata': self.extract_metadata(processed_image)
            }
            
            # Sauvegarder l'annotation
            self.save_annotation(annotation)
            
            print(f"Échantillon collecté: {sample_id} - {skin_type}")
            return True
            
        except Exception as e:
            print(f"Erreur collecte données: {e}")
            return False
    
    def preprocess_for_dataset(self, image_path):
        """Préprocessing standardisé pour le dataset"""
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError("Image invalide")
        
        # Détection et crop du visage
        face_region = self.detect_face_region(img)
        if face_region is None:
            # Fallback: région centrale
            h, w = img.shape[:2]
            center_y, center_x = h // 2, w // 2
            size = min(h, w) // 2
            face_region = img[
                max(0, center_y - size):min(h, center_y + size),
                max(0, center_x - size):min(w, center_x + size)
            ]
        
        # Redimensionner à 224x224 (standard pour EfficientNet)
        face_region = cv2.resize(face_region, (224, 224))
        
        # Correction d'éclairage
        face_region = self.correct_lighting(face_region)
        
        return face_region
    
    def detect_face_region(self, img):
        """Détection de visage pour le dataset"""
        try:
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(faces) > 0:
                x, y, w, h = max(faces, key=lambda f: f[2] * f[3])
                margin = int(min(w, h) * 0.3)  # Marge plus large pour plus de contexte
                x = max(0, x - margin)
                y = max(0, y - margin)
                w = min(img.shape[1] - x, w + 2 * margin)
                h = min(img.shape[0] - y, h + 2 * margin)
                return img[y:y+h, x:x+w]
            
            return None
        except:
            return None
    
    def correct_lighting(self, img):
        """Correction d'éclairage pour le dataset"""
        try:
            lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
            lab = cv2.merge([l, a, b])
            return cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        except:
            return img
    
    def assess_image_quality(self, img):
        """Évalue la qualité de l'image"""
        try:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Netteté (variance du Laplacien)
            sharpness = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            # Luminosité moyenne
            brightness = np.mean(gray)
            
            # Contraste (écart-type)
            contrast = np.std(gray)
            
            quality_score = min(100, max(0, 
                (sharpness / 1000) * 40 +  # Netteté (0-40 points)
                (min(brightness, 255-brightness) / 127.5) * 30 +  # Luminosité équilibrée (0-30 points)
                (contrast / 64) * 30  # Contraste (0-30 points)
            ))
            
            return {
                'score': float(quality_score),
                'sharpness': float(sharpness),
                'brightness': float(brightness),
                'contrast': float(contrast),
                'quality_level': 'high' if quality_score > 70 else 'medium' if quality_score > 40 else 'low'
            }
        except:
            return {'score': 50.0, 'quality_level': 'unknown'}
    
    def extract_metadata(self, img):
        """Extrait les métadonnées de l'image"""
        try:
            h, w, c = img.shape
            
            # Analyse des couleurs dominantes
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            mean_color = np.mean(img_rgb, axis=(0, 1))
            
            # Analyse de texture
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            texture_variance = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            return {
                'dimensions': {'width': w, 'height': h, 'channels': c},
                'mean_color': mean_color.tolist(),
                'texture_variance': float(texture_variance),
                'file_size_estimate': w * h * c  # Estimation de la taille
            }
        except:
            return {}
    
    def save_annotation(self, annotation):
        """Sauvegarde une annotation dans le fichier JSON"""
        try:
            # Charger les annotations existantes
            annotations = []
            if os.path.exists(self.annotations_file):
                with open(self.annotations_file, 'r', encoding='utf-8') as f:
                    annotations = json.load(f)
            
            # Ajouter la nouvelle annotation
            annotations.append(annotation)
            
            # Sauvegarder
            with open(self.annotations_file, 'w', encoding='utf-8') as f:
                json.dump(annotations, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Erreur sauvegarde annotation: {e}")
    
    def get_dataset_stats(self):
        """Retourne les statistiques du dataset"""
        try:
            if not os.path.exists(self.annotations_file):
                return {'total': 0, 'by_type': {}}
            
            with open(self.annotations_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)
            
            stats = {'total': len(annotations), 'by_type': {}}
            
            for annotation in annotations:
                skin_type = annotation['skin_type']
                stats['by_type'][skin_type] = stats['by_type'].get(skin_type, 0) + 1
            
            return stats
            
        except Exception as e:
            print(f"Erreur stats dataset: {e}")
            return {'total': 0, 'by_type': {}}
    
    def create_data_augmentation(self, image_path, skin_type, num_augmentations=5):
        """Crée des variations d'une image pour augmenter le dataset"""
        try:
            img = cv2.imread(image_path)
            if img is None:
                return False
            
            augmentations = [
                self.flip_horizontal,
                self.adjust_brightness,
                self.add_noise,
                self.slight_rotation,
                self.zoom_crop
            ]
            
            for i in range(min(num_augmentations, len(augmentations))):
                augmented_img = augmentations[i](img.copy())
                
                # Sauvegarder l'image augmentée
                sample_id = str(uuid.uuid4())
                target_path = os.path.join(self.dataset_path, skin_type, f"{sample_id}_aug.jpg")
                cv2.imwrite(target_path, augmented_img)
                
                # Créer l'annotation
                annotation = {
                    'id': sample_id,
                    'filename': f"{skin_type}/{sample_id}_aug.jpg",
                    'skin_type': skin_type,
                    'timestamp': datetime.now().isoformat(),
                    'is_augmented': True,
                    'augmentation_type': augmentations[i].__name__,
                    'image_quality': self.assess_image_quality(augmented_img)
                }
                
                self.save_annotation(annotation)
            
            return True
            
        except Exception as e:
            print(f"Erreur augmentation: {e}")
            return False
    
    def flip_horizontal(self, img):
        """Retournement horizontal"""
        return cv2.flip(img, 1)
    
    def adjust_brightness(self, img):
        """Ajustement de luminosité"""
        factor = np.random.uniform(0.7, 1.3)
        return cv2.convertScaleAbs(img, alpha=factor, beta=0)
    
    def add_noise(self, img):
        """Ajout de bruit léger"""
        noise = np.random.normal(0, 10, img.shape).astype(np.uint8)
        return cv2.add(img, noise)
    
    def slight_rotation(self, img):
        """Rotation légère"""
        angle = np.random.uniform(-5, 5)
        h, w = img.shape[:2]
        center = (w // 2, h // 2)
        matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        return cv2.warpAffine(img, matrix, (w, h))
    
    def zoom_crop(self, img):
        """Zoom et crop"""
        h, w = img.shape[:2]
        zoom_factor = np.random.uniform(1.1, 1.3)
        new_h, new_w = int(h * zoom_factor), int(w * zoom_factor)
        
        # Redimensionner
        resized = cv2.resize(img, (new_w, new_h))
        
        # Crop au centre
        start_x = (new_w - w) // 2
        start_y = (new_h - h) // 2
        return resized[start_y:start_y+h, start_x:start_x+w]

# Instance globale du collecteur
data_collector = DataCollector()

import numpy as np
from .models import SkinType, SkinAnalysisQuestion

def analyze_form_responses(responses, questions):
    """
    Analyse les réponses du formulaire pour déterminer le type de peau
    
    Args:
        responses: dict des réponses {question_id: response}
        questions: QuerySet des questions
    
    Returns:
        tuple: (SkinType object, confidence_score)
    """
    
    # Scores pour chaque type de peau
    skin_type_scores = {
        'oily': 0,
        'dry': 0,
        'combination': 0,
        'sensitive': 0,
        'normal': 0,
        'acne_prone': 0,
    }
    
    total_weight = 0
    
    for question in questions:
        if question.id in responses:
            response = responses[question.id]
            weight = question.weight
            total_weight += weight
            
            # Appliquer le mapping de la question
            if question.skin_type_mapping:
                for skin_type, response_mapping in question.skin_type_mapping.items():
                    if isinstance(response_mapping, list):
                        if response in response_mapping:
                            skin_type_scores[skin_type] += weight
                    elif response == response_mapping:
                        skin_type_scores[skin_type] += weight
    
    # Normaliser les scores
    if total_weight > 0:
        for skin_type in skin_type_scores:
            skin_type_scores[skin_type] = skin_type_scores[skin_type] / total_weight
    
    # Trouver le type de peau avec le score le plus élevé
    best_skin_type = max(skin_type_scores, key=skin_type_scores.get)
    confidence = skin_type_scores[best_skin_type]
    
    # Récupérer l'objet SkinType
    try:
        skin_type_obj = SkinType.objects.get(name=best_skin_type)
    except SkinType.DoesNotExist:
        # Fallback vers peau normale
        skin_type_obj = SkinType.objects.get(name='normal')
        confidence = 0.5
    
    return skin_type_obj, confidence

def analyze_image_basic(image_path):
    """
    Analyse basique d'image pour détecter le type de peau
    Cette fonction peut être améliorée avec des modèles ML plus sophistiqués
    
    Args:
        image_path: chemin vers l'image
    
    Returns:
        tuple: (skin_type_name, confidence, analysis_data)
    """
    try:
        import cv2
        from PIL import Image
        
        # Charger l'image
        img = cv2.imread(image_path)
        if img is None:
            return 'normal', 0.5, {'error': 'Impossible de charger l\'image'}
        
        # Convertir en RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Analyse basique de la couleur et texture
        # Calculer la moyenne des couleurs
        mean_color = np.mean(img_rgb, axis=(0, 1))
        
        # Calculer la variance (indicateur de texture)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        variance = np.var(gray)
        
        # Détecter les zones brillantes (indicateur de peau grasse)
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        brightness = np.mean(hsv[:, :, 2])
        
        analysis_data = {
            'mean_color': mean_color.tolist(),
            'texture_variance': float(variance),
            'brightness': float(brightness),
        }
        
        # Logique simple de classification
        if brightness > 150 and variance > 1000:
            return 'oily', 0.7, analysis_data
        elif brightness < 100 and variance < 500:
            return 'dry', 0.6, analysis_data
        elif variance > 1500:
            return 'acne_prone', 0.65, analysis_data
        else:
            return 'normal', 0.6, analysis_data
            
    except ImportError:
        # Si OpenCV n'est pas disponible, retourner une analyse par défaut
        return 'normal', 0.5, {'error': 'OpenCV non disponible'}
    except Exception as e:
        return 'normal', 0.5, {'error': str(e)}

def get_skin_care_recommendations(skin_type):
    """
    Retourne des recommandations de soins pour un type de peau donné
    
    Args:
        skin_type: objet SkinType
    
    Returns:
        dict: recommandations structurées
    """
    
    recommendations = {
        'oily': {
            'routine': [
                'Nettoyant doux sans huile',
                'Tonique astringent',
                'Sérum à l\'acide salicylique',
                'Hydratant léger non-comédogène',
                'Protection solaire matifiante'
            ],
            'ingredients_to_look_for': [
                'Acide salicylique',
                'Niacinamide',
                'Zinc',
                'Argile'
            ],
            'ingredients_to_avoid': [
                'Huiles lourdes',
                'Alcool dénaturé',
                'Parfums forts'
            ]
        },
        'dry': {
            'routine': [
                'Nettoyant crémeux hydratant',
                'Tonique hydratant',
                'Sérum à l\'acide hyaluronique',
                'Crème hydratante riche',
                'Protection solaire hydratante'
            ],
            'ingredients_to_look_for': [
                'Acide hyaluronique',
                'Céramides',
                'Glycérine',
                'Beurre de karité'
            ],
            'ingredients_to_avoid': [
                'Alcool',
                'Parfums',
                'Sulfates agressifs'
            ]
        },
        'sensitive': {
            'routine': [
                'Nettoyant très doux',
                'Tonique sans alcool',
                'Sérum apaisant',
                'Crème hypoallergénique',
                'Protection solaire minérale'
            ],
            'ingredients_to_look_for': [
                'Aloe vera',
                'Camomille',
                'Centella asiatica',
                'Niacinamide'
            ],
            'ingredients_to_avoid': [
                'Parfums',
                'Alcool',
                'Acides forts',
                'Rétinol'
            ]
        }
    }
    
    return recommendations.get(skin_type.name, recommendations['normal'] if 'normal' in recommendations else {})

def create_default_questions():
    """Crée les questions par défaut pour l'analyse de peau"""
    
    questions_data = [
        {
            'question_text': 'Comment décririez-vous votre peau le matin au réveil ?',
            'question_type': 'single_choice',
            'choices': ['Très grasse', 'Légèrement grasse', 'Normale', 'Légèrement sèche', 'Très sèche'],
            'weight': 2.0,
            'skin_type_mapping': {
                'oily': ['Très grasse', 'Légèrement grasse'],
                'dry': ['Très sèche', 'Légèrement sèche'],
                'normal': ['Normale']
            },
            'order': 1
        },
        {
            'question_text': 'À quelle fréquence avez-vous des boutons ou points noirs ?',
            'question_type': 'single_choice',
            'choices': ['Très souvent', 'Souvent', 'Parfois', 'Rarement', 'Jamais'],
            'weight': 1.5,
            'skin_type_mapping': {
                'acne_prone': ['Très souvent', 'Souvent'],
                'oily': ['Parfois'],
                'normal': ['Rarement', 'Jamais']
            },
            'order': 2
        },
        {
            'question_text': 'Comment votre peau réagit-elle aux nouveaux produits ?',
            'question_type': 'single_choice',
            'choices': ['Très sensible', 'Sensible', 'Normale', 'Tolérante'],
            'weight': 1.8,
            'skin_type_mapping': {
                'sensitive': ['Très sensible', 'Sensible'],
                'normal': ['Normale', 'Tolérante']
            },
            'order': 3
        }
    ]
    
    for q_data in questions_data:
        question, created = SkinAnalysisQuestion.objects.get_or_create(
            question_text=q_data['question_text'],
            defaults=q_data
        )
        if created:
            print(f"Question créée: {question.question_text}")

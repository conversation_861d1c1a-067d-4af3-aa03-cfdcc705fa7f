import numpy as np
from .models import SkinType, SkinAnalysisQuestion

def analyze_form_responses(responses, questions):
    """
    Analyse les réponses du formulaire pour déterminer le type de peau
    
    Args:
        responses: dict des réponses {question_id: response}
        questions: QuerySet des questions
    
    Returns:
        tuple: (SkinType object, confidence_score)
    """
    
    # Scores pour chaque type de peau
    skin_type_scores = {
        'oily': 0,
        'dry': 0,
        'combination': 0,
        'sensitive': 0,
        'normal': 0,
        'acne_prone': 0,
    }
    
    total_weight = 0
    
    for question in questions:
        if question.id in responses:
            response = responses[question.id]
            weight = question.weight
            total_weight += weight
            
            # Appliquer le mapping de la question
            if question.skin_type_mapping:
                for skin_type, response_mapping in question.skin_type_mapping.items():
                    if isinstance(response_mapping, list):
                        if response in response_mapping:
                            skin_type_scores[skin_type] += weight
                    elif response == response_mapping:
                        skin_type_scores[skin_type] += weight
    
    # Normaliser les scores
    if total_weight > 0:
        for skin_type in skin_type_scores:
            skin_type_scores[skin_type] = skin_type_scores[skin_type] / total_weight
    
    # Trouver le type de peau avec le score le plus élevé
    best_skin_type = max(skin_type_scores, key=skin_type_scores.get)
    confidence = skin_type_scores[best_skin_type]
    
    # Récupérer l'objet SkinType
    try:
        skin_type_obj = SkinType.objects.get(name=best_skin_type)
    except SkinType.DoesNotExist:
        # Fallback vers peau normale
        skin_type_obj = SkinType.objects.get(name='normal')
        confidence = 0.5
    
    return skin_type_obj, confidence

def analyze_image_basic(image_path):
    """
    Analyse basique d'image pour détecter le type de peau
    Optimisée pour fonctionner même avec des images de faible qualité

    Args:
        image_path: chemin vers l'image

    Returns:
        tuple: (skin_type_name, confidence, analysis_data)
    """
    try:
        import cv2
        import numpy as np
        from PIL import Image as PILImage

        # Charger l'image avec PIL d'abord pour validation
        try:
            pil_img = PILImage.open(image_path)
            pil_img.verify()  # Vérifier l'intégrité
            pil_img = PILImage.open(image_path)  # Recharger après verify
        except Exception as e:
            return 'normal', 0.3, {'error': f'Image corrompue: {str(e)}'}

        # Charger avec OpenCV
        img = cv2.imread(image_path)
        if img is None:
            return 'normal', 0.3, {'error': 'Impossible de charger l\'image avec OpenCV'}

        # Vérifier les dimensions minimales
        height, width = img.shape[:2]
        if width < 100 or height < 100:
            return 'normal', 0.2, {'error': 'Image trop petite pour l\'analyse'}

        # Redimensionner si l'image est trop grande (optimisation)
        if width > 1024 or height > 1024:
            scale = min(1024/width, 1024/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = cv2.resize(img, (new_width, new_height))

        # Convertir en RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Détecter et analyser la zone du visage (région centrale)
        center_y, center_x = img.shape[0] // 2, img.shape[1] // 2
        face_region_size = min(img.shape[0], img.shape[1]) // 3

        y1 = max(0, center_y - face_region_size // 2)
        y2 = min(img.shape[0], center_y + face_region_size // 2)
        x1 = max(0, center_x - face_region_size // 2)
        x2 = min(img.shape[1], center_x + face_region_size // 2)

        face_region = img_rgb[y1:y2, x1:x2]

        # Analyse de la couleur sur la région du visage
        mean_color = np.mean(face_region, axis=(0, 1))
        std_color = np.std(face_region, axis=(0, 1))

        # Analyse de la texture
        gray_face = cv2.cvtColor(face_region, cv2.COLOR_RGB2GRAY)

        # Calculer la variance locale (indicateur de texture/imperfections)
        laplacian_var = cv2.Laplacian(gray_face, cv2.CV_64F).var()

        # Analyse HSV pour la brillance et saturation
        hsv_face = cv2.cvtColor(face_region, cv2.COLOR_RGB2HSV)
        mean_brightness = np.mean(hsv_face[:, :, 2])
        mean_saturation = np.mean(hsv_face[:, :, 1])

        # Détection des zones brillantes (indicateur de sébum)
        bright_threshold = np.percentile(hsv_face[:, :, 2], 85)
        bright_pixels = np.sum(hsv_face[:, :, 2] > bright_threshold)
        bright_ratio = bright_pixels / (face_region.shape[0] * face_region.shape[1])

        # Analyse des rougeurs (indicateur de sensibilité)
        red_dominance = mean_color[0] / (mean_color[1] + mean_color[2] + 1)

        analysis_data = {
            'mean_color': mean_color.tolist(),
            'color_std': std_color.tolist(),
            'texture_variance': float(laplacian_var),
            'brightness': float(mean_brightness),
            'saturation': float(mean_saturation),
            'bright_ratio': float(bright_ratio),
            'red_dominance': float(red_dominance),
            'image_quality': 'good' if laplacian_var > 100 else 'low',
            'face_region_size': f"{face_region.shape[1]}x{face_region.shape[0]}"
        }

        # Logique de classification améliorée
        confidence = 0.5  # Base confidence

        # Ajustement de confiance basé sur la qualité
        if laplacian_var > 200:
            confidence += 0.2
        elif laplacian_var < 50:
            confidence -= 0.2

        # Analyse avancée pour détection d'acné et imperfections
        # Détection de contours pour identifier les imperfections
        edges = cv2.Canny(gray_face, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Compter les petites imperfections (boutons, points noirs)
        small_imperfections = 0
        large_imperfections = 0
        for contour in contours:
            area = cv2.contourArea(contour)
            if 5 < area < 50:  # Petites imperfections
                small_imperfections += 1
            elif 50 < area < 200:  # Grandes imperfections
                large_imperfections += 1

        # Analyse de texture locale pour détecter les pores dilatés
        kernel = np.ones((3,3), np.uint8)
        tophat = cv2.morphologyEx(gray_face, cv2.MORPH_TOPHAT, kernel)
        pore_density = np.sum(tophat > 30) / (gray_face.shape[0] * gray_face.shape[1])

        # Analyse des zones T (front, nez, menton) vs joues
        h, w = face_region.shape[:2]
        t_zone = face_region[h//4:3*h//4, w//3:2*w//3]  # Zone centrale
        cheek_zones = [
            face_region[h//3:2*h//3, :w//3],  # Joue gauche
            face_region[h//3:2*h//3, 2*w//3:]  # Joue droite
        ]

        t_zone_brightness = np.mean(cv2.cvtColor(t_zone, cv2.COLOR_RGB2HSV)[:, :, 2])
        cheek_brightness = np.mean([np.mean(cv2.cvtColor(cheek, cv2.COLOR_RGB2HSV)[:, :, 2])
                                   for cheek in cheek_zones if cheek.size > 0])

        brightness_diff = abs(t_zone_brightness - cheek_brightness)

        # Classification basée sur plusieurs critères avancés
        oily_score = 0
        dry_score = 0
        sensitive_score = 0
        acne_score = 0
        combination_score = 0
        normal_score = 0

        # Critères pour peau acnéique (priorité haute)
        if small_imperfections > 15:
            acne_score += 4
        elif small_imperfections > 8:
            acne_score += 2
        elif small_imperfections > 3:
            acne_score += 1

        if large_imperfections > 5:
            acne_score += 3
        elif large_imperfections > 2:
            acne_score += 2

        if laplacian_var > 1500:  # Très haute variance = imperfections importantes
            acne_score += 3
        elif laplacian_var > 1000:
            acne_score += 2

        if pore_density > 0.15:  # Pores dilatés
            acne_score += 2
            oily_score += 1

        # Critères pour peau grasse
        if bright_ratio > 0.35:
            oily_score += 3
        elif bright_ratio > 0.25:
            oily_score += 2
        elif bright_ratio > 0.15:
            oily_score += 1

        if mean_brightness > 150:
            oily_score += 2
        elif mean_brightness > 130:
            oily_score += 1

        if pore_density > 0.1:
            oily_score += 1

        # Critères pour peau sèche
        if bright_ratio < 0.05:
            dry_score += 3
        elif bright_ratio < 0.1:
            dry_score += 2

        if mean_brightness < 90:
            dry_score += 2
        elif mean_brightness < 110:
            dry_score += 1

        if mean_saturation < 40:
            dry_score += 2
        elif mean_saturation < 60:
            dry_score += 1

        if laplacian_var < 300:  # Texture très lisse = peau sèche
            dry_score += 1

        # Critères pour peau sensible
        if red_dominance > 1.3:
            sensitive_score += 3
        elif red_dominance > 1.15:
            sensitive_score += 2
        elif red_dominance > 1.05:
            sensitive_score += 1

        if std_color[0] > 35:  # Variation de rouge élevée
            sensitive_score += 2
        elif std_color[0] > 25:
            sensitive_score += 1

        # Critères pour peau mixte
        if brightness_diff > 20:
            combination_score += 3
        elif brightness_diff > 15:
            combination_score += 2
        elif brightness_diff > 10:
            combination_score += 1

        if t_zone_brightness > cheek_brightness + 15:
            combination_score += 2
            oily_score += 1

        # Critères pour peau normale
        if 0.1 <= bright_ratio <= 0.2 and 110 <= mean_brightness <= 140:
            normal_score += 2
        if 500 <= laplacian_var <= 1000:
            normal_score += 1
        if small_imperfections <= 3 and large_imperfections == 0:
            normal_score += 2

        # Déterminer le type de peau avec logique avancée
        scores = {
            'acne_prone': acne_score,
            'oily': oily_score,
            'dry': dry_score,
            'combination': combination_score,
            'sensitive': sensitive_score,
            'normal': normal_score
        }

        # Logique de décision hiérarchique
        # 1. Priorité à l'acné si score élevé
        if acne_score >= 4:
            skin_type = 'acne_prone'
            confidence = 0.85 + min(0.1, acne_score * 0.02)
        elif acne_score >= 2 and acne_score > max(oily_score, dry_score, combination_score):
            skin_type = 'acne_prone'
            confidence = 0.75 + min(0.1, acne_score * 0.02)
        # 2. Peau mixte si différence T-zone/joues significative
        elif combination_score >= 3:
            skin_type = 'combination'
            confidence = 0.8 + min(0.1, combination_score * 0.02)
        # 3. Peau sensible si rougeurs importantes
        elif sensitive_score >= 3:
            skin_type = 'sensitive'
            confidence = 0.75 + min(0.1, sensitive_score * 0.02)
        # 4. Peau grasse ou sèche
        elif oily_score >= 3:
            skin_type = 'oily'
            confidence = 0.7 + min(0.15, oily_score * 0.03)
        elif dry_score >= 3:
            skin_type = 'dry'
            confidence = 0.7 + min(0.15, dry_score * 0.03)
        # 5. Scores moyens - comparer les plus hauts
        else:
            max_score = max(scores.values())
            if max_score >= 2:
                skin_type = max(scores, key=scores.get)
                confidence = 0.6 + min(0.2, max_score * 0.05)
            else:
                skin_type = 'normal'
                confidence = 0.5 + normal_score * 0.05

        # Ajustements de confiance basés sur la qualité de l'image
        if laplacian_var > 200:
            confidence += 0.1  # Image nette
        elif laplacian_var < 50:
            confidence -= 0.15  # Image floue

        # Ajustement si beaucoup d'imperfections détectées
        if small_imperfections > 10 or large_imperfections > 3:
            if skin_type != 'acne_prone':
                # Forcer vers acné si beaucoup d'imperfections mais pas détecté
                skin_type = 'acne_prone'
                confidence = 0.8

        # Limiter la confiance entre 0.3 et 0.95
        confidence = min(0.95, max(0.3, confidence))

        # Données détaillées pour debug
        analysis_data.update({
            'scores': scores,
            'imperfections': {
                'small': small_imperfections,
                'large': large_imperfections,
                'pore_density': float(pore_density)
            },
            'zones_analysis': {
                't_zone_brightness': float(t_zone_brightness),
                'cheek_brightness': float(cheek_brightness),
                'brightness_difference': float(brightness_diff)
            },
            'final_decision': {
                'primary_score': max(scores.values()),
                'decision_reason': f"Score {skin_type}: {scores[skin_type]}"
            }
        })

        return skin_type, confidence, analysis_data

    except ImportError as e:
        return 'normal', 0.2, {'error': f'Bibliothèques manquantes: {str(e)}'}
    except Exception as e:
        import traceback
        error_msg = f'Erreur d\'analyse: {str(e)}'
        print(f"Error in analyze_image_basic: {error_msg}")
        print(traceback.format_exc())
        return 'normal', 0.2, {'error': error_msg, 'traceback': traceback.format_exc()}

def get_skin_care_recommendations(skin_type):
    """
    Retourne des recommandations de soins pour un type de peau donné
    
    Args:
        skin_type: objet SkinType
    
    Returns:
        dict: recommandations structurées
    """
    
    recommendations = {
        'oily': {
            'routine': [
                'Nettoyant doux sans huile',
                'Tonique astringent',
                'Sérum à l\'acide salicylique',
                'Hydratant léger non-comédogène',
                'Protection solaire matifiante'
            ],
            'ingredients_to_look_for': [
                'Acide salicylique',
                'Niacinamide',
                'Zinc',
                'Argile'
            ],
            'ingredients_to_avoid': [
                'Huiles lourdes',
                'Alcool dénaturé',
                'Parfums forts'
            ]
        },
        'dry': {
            'routine': [
                'Nettoyant crémeux hydratant',
                'Tonique hydratant',
                'Sérum à l\'acide hyaluronique',
                'Crème hydratante riche',
                'Protection solaire hydratante'
            ],
            'ingredients_to_look_for': [
                'Acide hyaluronique',
                'Céramides',
                'Glycérine',
                'Beurre de karité'
            ],
            'ingredients_to_avoid': [
                'Alcool',
                'Parfums',
                'Sulfates agressifs'
            ]
        },
        'sensitive': {
            'routine': [
                'Nettoyant très doux',
                'Tonique sans alcool',
                'Sérum apaisant',
                'Crème hypoallergénique',
                'Protection solaire minérale'
            ],
            'ingredients_to_look_for': [
                'Aloe vera',
                'Camomille',
                'Centella asiatica',
                'Niacinamide'
            ],
            'ingredients_to_avoid': [
                'Parfums',
                'Alcool',
                'Acides forts',
                'Rétinol'
            ]
        }
    }
    
    return recommendations.get(skin_type.name, recommendations['normal'] if 'normal' in recommendations else {})

def create_default_questions():
    """Crée les questions par défaut pour l'analyse de peau"""
    
    questions_data = [
        {
            'question_text': 'Comment décririez-vous votre peau le matin au réveil ?',
            'question_type': 'single_choice',
            'choices': ['Très grasse', 'Légèrement grasse', 'Normale', 'Légèrement sèche', 'Très sèche'],
            'weight': 2.0,
            'skin_type_mapping': {
                'oily': ['Très grasse', 'Légèrement grasse'],
                'dry': ['Très sèche', 'Légèrement sèche'],
                'normal': ['Normale']
            },
            'order': 1
        },
        {
            'question_text': 'À quelle fréquence avez-vous des boutons ou points noirs ?',
            'question_type': 'single_choice',
            'choices': ['Très souvent', 'Souvent', 'Parfois', 'Rarement', 'Jamais'],
            'weight': 1.5,
            'skin_type_mapping': {
                'acne_prone': ['Très souvent', 'Souvent'],
                'oily': ['Parfois'],
                'normal': ['Rarement', 'Jamais']
            },
            'order': 2
        },
        {
            'question_text': 'Comment votre peau réagit-elle aux nouveaux produits ?',
            'question_type': 'single_choice',
            'choices': ['Très sensible', 'Sensible', 'Normale', 'Tolérante'],
            'weight': 1.8,
            'skin_type_mapping': {
                'sensitive': ['Très sensible', 'Sensible'],
                'normal': ['Normale', 'Tolérante']
            },
            'order': 3
        }
    ]
    
    for q_data in questions_data:
        question, created = SkinAnalysisQuestion.objects.get_or_create(
            question_text=q_data['question_text'],
            defaults=q_data
        )
        if created:
            print(f"Question créée: {question.question_text}")

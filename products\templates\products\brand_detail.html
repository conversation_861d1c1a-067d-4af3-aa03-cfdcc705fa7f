{% extends 'skin_analysis/base.html' %}

{% block title %}{{ brand.name }} - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" style="border-radius: 15px;">
                <div class="card-body text-center">
                    {% if brand.logo %}
                    <img src="{{ brand.logo.url }}" alt="{{ brand.name }}" style="max-height: 100px; margin-bottom: 20px;">
                    {% endif %}
                    
                    <h1>{{ brand.name }}</h1>
                    <p class="lead">{{ brand.description }}</p>
                    
                    <div class="mb-3">
                        {% if brand.is_tunisian %}
                        <span class="badge bg-success p-2">
                            <i class="fas fa-flag"></i> Marque Tunisienne
                        </span>
                        {% else %}
                        <span class="badge bg-primary p-2">
                            <i class="fas fa-globe"></i> Disponible en Tunisie
                        </span>
                        {% endif %}
                    </div>
                    
                    {% if brand.website %}
                    <a href="{{ brand.website }}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Visiter le Site Web
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <h3 class="text-white mb-4">Produits {{ brand.name }}</h3>
            
            {% if products %}
            <div class="row">
                {% for product in products %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100" style="border-radius: 15px;">
                        {% if product.image %}
                        <img src="{{ product.image.url }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ product.name }}">
                        {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                        {% endif %}
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ product.name }}</h5>
                            <p class="card-text">{{ product.description|truncatewords:15 }}</p>
                            
                            <div class="mb-3">
                                {% for skin_type in product.suitable_skin_types.all %}
                                <span class="badge bg-primary me-1">{{ skin_type.get_name_display }}</span>
                                {% endfor %}
                            </div>
                            
                            {% if product.get_current_price %}
                            <div class="mb-3">
                                <span class="h5 text-success">{{ product.get_current_price|floatformat:3 }} TND</span>
                            </div>
                            {% endif %}
                            
                            <a href="{% url 'products:detail' product.id %}" class="btn btn-primary">
                                <i class="fas fa-eye"></i> Voir Détails
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95); padding: 40px;">
                <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                <h4>Aucun Produit Disponible</h4>
                <p class="text-muted">Cette marque n'a pas encore de produits référencés.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

"""
Commande pour tester la validation de visage/peau
"""

import os
from django.core.management.base import BaseCommand
from skin_analysis.face_skin_detector import face_skin_detector

class Command(BaseCommand):
    help = 'Test la validation de visage/peau'

    def add_arguments(self, parser):
        parser.add_argument(
            '--image-path',
            type=str,
            help='Chemin vers une image spécifique à tester'
        )
        parser.add_argument(
            '--test-dataset',
            action='store_true',
            help='Tester sur quelques images du dataset'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Test du Système de Validation Visage/Peau'))
        
        if options['image_path']:
            self.test_single_image(options['image_path'])
        elif options['test_dataset']:
            self.test_dataset_images()
        else:
            self.stdout.write(self.style.WARNING('Utilisez --image-path ou --test-dataset'))
    
    def test_single_image(self, image_path):
        """Test une image spécifique"""
        if not os.path.exists(image_path):
            self.stdout.write(self.style.ERROR(f'❌ Image non trouvée: {image_path}'))
            return
        
        self.stdout.write(f'📁 Test de: {image_path}')
        
        is_valid, message = face_skin_detector.validate_image_for_skin_analysis(image_path)
        
        if is_valid:
            self.stdout.write(self.style.SUCCESS(f'✅ {message}'))
        else:
            self.stdout.write(self.style.ERROR(f'❌ {message}'))
            
            # Afficher les suggestions
            suggestions = face_skin_detector.get_validation_suggestions(image_path)
            self.stdout.write('\n💡 Suggestions:')
            for suggestion in suggestions:
                self.stdout.write(f'  • {suggestion}')
    
    def test_dataset_images(self):
        """Test quelques images du dataset"""
        dataset_path = 'media/real_datasets/synthetic_demo'
        
        if not os.path.exists(dataset_path):
            self.stdout.write(self.style.ERROR(f'❌ Dataset non trouvé: {dataset_path}'))
            return
        
        # Tester quelques images de chaque type
        skin_types = ['normal', 'oily', 'dry', 'combination', 'sensitive', 'acne_prone']
        
        for skin_type in skin_types:
            type_path = os.path.join(dataset_path, skin_type)
            if os.path.exists(type_path):
                images = [f for f in os.listdir(type_path) if f.endswith(('.jpg', '.jpeg', '.png'))]
                if images:
                    # Tester la première image de ce type
                    test_image = os.path.join(type_path, images[0])
                    self.stdout.write(f'\n🧪 Test {skin_type}: {images[0]}')
                    
                    is_valid, message = face_skin_detector.validate_image_for_skin_analysis(test_image)
                    
                    if is_valid:
                        self.stdout.write(self.style.SUCCESS(f'  ✅ {message}'))
                    else:
                        self.stdout.write(self.style.WARNING(f'  ⚠️ {message}'))
        
        self.stdout.write(self.style.SUCCESS('\n🎯 Test de validation terminé'))

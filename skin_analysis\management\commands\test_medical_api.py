"""
Commande pour tester l'API médicale
"""

import os
import requests
from django.core.management.base import BaseCommand
from django.conf import settings

class Command(BaseCommand):
    help = 'Test l\'API médicale de grade hospitalier'

    def add_arguments(self, parser):
        parser.add_argument(
            '--image-path',
            type=str,
            help='Chemin vers une image à tester'
        )
        parser.add_argument(
            '--server-url',
            type=str,
            default='http://127.0.0.1:8000',
            help='URL du serveur (défaut: http://127.0.0.1:8000)'
        )

    def handle(self, *args, **options):
        server_url = options['server_url']
        image_path = options.get('image_path')
        
        self.stdout.write(self.style.SUCCESS('🏥 Test de l\'API Médicale'))
        self.stdout.write(f'🌐 Serveur: {server_url}')
        
        if not image_path:
            # Utiliser une image du dataset par défaut
            dataset_path = 'media/real_datasets/synthetic_demo/normal'
            if os.path.exists(dataset_path):
                images = [f for f in os.listdir(dataset_path) if f.endswith(('.jpg', '.jpeg', '.png'))]
                if images:
                    image_path = os.path.join(dataset_path, images[0])
                    self.stdout.write(f'📁 Image par défaut: {image_path}')
        
        if not image_path or not os.path.exists(image_path):
            self.stdout.write(self.style.ERROR('❌ Aucune image valide trouvée'))
            self.stdout.write('💡 Utilisez --image-path pour spécifier une image')
            return
        
        self.test_medical_api(server_url, image_path)
    
    def test_medical_api(self, server_url, image_path):
        """Test l'API médicale avec une image"""
        api_url = f"{server_url}/api/medical-analysis/"
        
        try:
            self.stdout.write(f'🔬 Test API: {api_url}')
            self.stdout.write(f'📸 Image: {image_path}')
            
            # Préparer la requête
            with open(image_path, 'rb') as image_file:
                files = {'image': image_file}
                data = {'medical_grade': 'hospital'}
                
                # Envoyer la requête
                self.stdout.write('📤 Envoi de la requête...')
                response = requests.post(api_url, files=files, data=data, timeout=30)
            
            # Analyser la réponse
            self.stdout.write(f'📥 Code de réponse: {response.status_code}')
            
            if response.status_code == 200:
                result = response.json()
                self.display_medical_results(result)
            else:
                self.stdout.write(self.style.ERROR(f'❌ Erreur API: {response.status_code}'))
                try:
                    error_data = response.json()
                    self.stdout.write(f'📋 Détails: {error_data}')
                except:
                    self.stdout.write(f'📋 Réponse brute: {response.text[:500]}')
        
        except requests.exceptions.ConnectionError:
            self.stdout.write(self.style.ERROR('❌ Impossible de se connecter au serveur'))
            self.stdout.write('💡 Assurez-vous que le serveur Django est démarré')
        except requests.exceptions.Timeout:
            self.stdout.write(self.style.ERROR('❌ Timeout de la requête'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Erreur: {e}'))
    
    def display_medical_results(self, result):
        """Affiche les résultats médicaux"""
        self.stdout.write(self.style.SUCCESS('✅ Analyse médicale réussie'))
        
        # Diagnostic principal
        primary_diagnosis = result.get('primary_diagnosis', 'Non déterminé')
        confidence = result.get('confidence', 0)
        self.stdout.write(f'🎯 Diagnostic: {primary_diagnosis}')
        self.stdout.write(f'📊 Confiance: {confidence:.1%}')
        
        # Grade médical
        medical_grade = result.get('medical_grade', 'N/A')
        analysis_method = result.get('analysis_method', 'N/A')
        self.stdout.write(f'🏥 Grade: {medical_grade}')
        self.stdout.write(f'🔬 Méthode: {analysis_method}')
        
        # Pourcentages
        if 'percentages' in result:
            self.stdout.write('\n📈 Répartition diagnostique:')
            percentages = result['percentages']
            for skin_type, percentage in sorted(percentages.items(), key=lambda x: x[1], reverse=True):
                if percentage > 0:
                    self.stdout.write(f'  • {skin_type}: {percentage}%')
        
        # Notes cliniques
        if 'clinical_notes' in result and result['clinical_notes']:
            self.stdout.write('\n📋 Notes cliniques:')
            for note in result['clinical_notes']:
                self.stdout.write(f'  • {note}')
        
        # Signes pathologiques
        if 'pathological_findings' in result and result['pathological_findings']:
            self.stdout.write('\n⚠️  Signes pathologiques:')
            for finding in result['pathological_findings']:
                self.stdout.write(f'  • {finding}')
        
        # Recommandations médicales
        if 'medical_recommendations' in result and result['medical_recommendations']:
            self.stdout.write('\n💊 Recommandations médicales:')
            for recommendation in result['medical_recommendations'][:5]:  # Limiter à 5
                self.stdout.write(f'  • {recommendation}')
        
        # Standards médicaux
        if 'medical_standards' in result:
            standards = result['medical_standards']
            self.stdout.write('\n🏥 Standards médicaux:')
            self.stdout.write(f'  • Grade: {standards.get("grade", "N/A")}')
            self.stdout.write(f'  • Confiance: {standards.get("confidence_range", "N/A")}')
            self.stdout.write(f'  • Validation clinique: {standards.get("clinical_validation", False)}')
            self.stdout.write(f'  • Détection pathologique: {standards.get("pathology_detection", False)}')
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Test API médicale terminé avec succès'))

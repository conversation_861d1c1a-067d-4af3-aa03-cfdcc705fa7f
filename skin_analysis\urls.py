from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'skin_analysis'

urlpatterns = [
    # Pages principales
    path('', views.home, name='home'),
    path('register/', views.register, name='register'),
    path('profile/', views.profile, name='profile'),
    
    # Analyse de peau
    path('analyze/form/', views.skin_analysis_form, name='form_analysis'),
    path('analyze/camera/', views.camera_analysis, name='camera_analysis'),
    path('camera-test/', views.camera_test, name='camera_test'),
    path('dataset-info/', views.dataset_info, name='dataset_info'),
    path('results/<int:analysis_id>/', views.analysis_results, name='results'),

    # Pages d'aide
    path('help/phone-camera/', views.phone_camera_help, name='phone_camera_help'),
    
    # API endpoints
    path('api/analyze-image/', views.analyze_image_api, name='analyze_image_api'),
    path('api/get-recommendations/', views.get_recommendations_api, name='get_recommendations_api'),
    path('api/feedback/', views.feedback_analysis, name='feedback_analysis'),
    path('api/ml-stats/', views.ml_model_stats, name='ml_model_stats'),
]

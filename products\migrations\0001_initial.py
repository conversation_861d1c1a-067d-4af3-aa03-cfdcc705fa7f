# Generated by Django 5.2.1 on 2025-06-25 13:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('skin_analysis', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('country', models.CharField(default='Tunisie', max_length=50)),
                ('website', models.URLField(blank=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='brands/')),
                ('is_tunisian', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productcategory')),
            ],
            options={
                'verbose_name_plural': 'Product Categories',
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('product_type', models.CharField(choices=[('cleanser', 'Nettoyant'), ('toner', 'Tonique'), ('serum', 'Sérum'), ('moisturizer', 'Hydratant'), ('sunscreen', 'Protection solaire'), ('treatment', 'Traitement'), ('mask', 'Masque'), ('exfoliant', 'Exfoliant'), ('oil', 'Huile'), ('other', 'Autre')], max_length=20)),
                ('description', models.TextField()),
                ('ingredients', models.TextField()),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/')),
                ('volume', models.CharField(blank=True, max_length=50)),
                ('usage_instructions', models.TextField(blank=True)),
                ('benefits', models.JSONField(default=list)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.brand')),
                ('suitable_skin_types', models.ManyToManyField(to='skin_analysis.skintype')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.productcategory')),
            ],
        ),
        migrations.CreateModel(
            name='ProductRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('priority', models.PositiveIntegerField(default=1)),
                ('reason', models.TextField()),
                ('created_by', models.CharField(default='system', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product')),
                ('skin_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='skin_analysis.skintype')),
            ],
            options={
                'ordering': ['priority', '-created_at'],
                'unique_together': {('skin_type', 'product')},
            },
        ),
    ]

from django import forms
from django.contrib.auth.models import User
from .models import UserProfile, SkinAnalysis, SkinType

class UserProfileForm(forms.ModelForm):
    """Formulaire pour le profil utilisateur"""
    
    class Meta:
        model = UserProfile
        fields = ['age', 'skin_concerns', 'allergies']
        widgets = {
            'age': forms.NumberInput(attrs={'class': 'form-control', 'min': 13, 'max': 100}),
            'skin_concerns': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Ex: acné, rides, taches, sensibilité...'}),
            'allergies': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Mentionnez vos allergies connues...'}),
        }

class SkinAnalysisForm(forms.ModelForm):
    """Formulaire pour l'analyse de peau"""
    
    class Meta:
        model = SkinAnalysis
        fields = ['image']
        widgets = {
            'image': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
        }

class QuestionResponseForm(forms.Form):
    """Formulaire dynamique pour les questions d'analyse"""
    
    def __init__(self, *args, **kwargs):
        questions = kwargs.pop('questions', [])
        super().__init__(*args, **kwargs)
        
        for question in questions:
            field_name = f'question_{question.id}'
            
            if question.question_type == 'single_choice':
                choices = [(choice, choice) for choice in question.choices]
                self.fields[field_name] = forms.ChoiceField(
                    label=question.question_text,
                    choices=choices,
                    widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
                    required=True
                )
            
            elif question.question_type == 'multiple_choice':
                choices = [(choice, choice) for choice in question.choices]
                self.fields[field_name] = forms.MultipleChoiceField(
                    label=question.question_text,
                    choices=choices,
                    widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'}),
                    required=False
                )
            
            elif question.question_type == 'scale':
                self.fields[field_name] = forms.IntegerField(
                    label=question.question_text,
                    min_value=1,
                    max_value=5,
                    widget=forms.NumberInput(attrs={'class': 'form-control', 'type': 'range', 'min': 1, 'max': 5}),
                    required=True
                )
            
            elif question.question_type == 'text':
                self.fields[field_name] = forms.CharField(
                    label=question.question_text,
                    widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
                    required=False
                )

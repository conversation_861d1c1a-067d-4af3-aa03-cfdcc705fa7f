{% extends 'skin_analysis/base.html' %}

{% block title %}Recommandations pour {{ skin_type.get_name_display }} - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="text-center mb-5">
        <h1 class="text-white">
            <i class="fas fa-star"></i> Recommandations pour 
            <span class="text-warning">{{ skin_type.get_name_display }}</span>
        </h1>
        <p class="text-white-50 lead">
            Produits spécialement sélectionnés pour votre type de peau
        </p>
    </div>
    
    {% if recommendations %}
    <div class="row">
        {% for rec in recommendations %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 15px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                {% if rec.product.image %}
                <img src="{{ rec.product.image.url }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ rec.product.name }}">
                {% else %}
                <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                    <i class="fas fa-image fa-3x text-muted"></i>
                </div>
                {% endif %}
                
                <div class="card-body d-flex flex-column">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title">{{ rec.product.name }}</h5>
                        <span class="badge bg-primary">Priorité {{ rec.priority }}</span>
                    </div>
                    
                    <p class="text-muted small mb-2">
                        <i class="fas fa-building"></i> {{ rec.product.brand.name }}
                    </p>
                    
                    <p class="card-text small flex-grow-1">{{ rec.reason }}</p>
                    
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-tag"></i> {{ rec.product.get_product_type_display }}
                        </small>
                        {% if rec.product.volume %}
                        <small class="text-muted ms-2">
                            <i class="fas fa-flask"></i> {{ rec.product.volume }}
                        </small>
                        {% endif %}
                    </div>
                    
                    {% if rec.product.benefits %}
                    <div class="mb-3">
                        {% for benefit in rec.product.benefits %}
                        <span class="badge bg-light text-dark me-1 mb-1">{{ benefit }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    {% if rec.product.get_current_price %}
                    <div class="mb-3">
                        <span class="h5 text-success">
                            <i class="fas fa-tag"></i> {{ rec.product.get_current_price }} TND
                        </span>
                    </div>
                    {% endif %}
                    
                    <div class="mt-auto">
                        <div class="d-grid gap-2">
                            <a href="{% url 'products:detail' rec.product.id %}" class="btn btn-primary">
                                <i class="fas fa-eye"></i> Voir Détails
                            </a>
                            <a href="{% url 'price_tracking:product_history' rec.product.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-chart-line"></i> Historique des Prix
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <div class="text-center mt-5">
        <a href="{% url 'products:list' %}?skin_type={{ skin_type.name }}" class="btn btn-outline-light btn-lg">
            <i class="fas fa-search"></i> Voir Tous les Produits pour {{ skin_type.get_name_display }}
        </a>
    </div>
    
    {% else %}
    <div class="text-center">
        <div class="card" style="border-radius: 15px; padding: 40px;">
            <i class="fas fa-search fa-4x text-muted mb-3"></i>
            <h3>Aucune recommandation trouvée</h3>
            <p class="text-muted">
                Nous n'avons pas encore de recommandations spécifiques pour ce type de peau.
            </p>
            <a href="{% url 'products:list' %}" class="btn btn-primary">
                <i class="fas fa-shopping-bag"></i> Voir Tous les Produits
            </a>
        </div>
    </div>
    {% endif %}
    
    <!-- Skin Care Tips -->
    <div class="mt-5">
        <div class="card" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
            <div class="card-body p-4">
                <h4 class="text-center mb-4">
                    <i class="fas fa-lightbulb text-warning"></i> 
                    Conseils pour {{ skin_type.get_name_display }}
                </h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success"></i> À Faire</h6>
                        <ul class="list-unstyled">
                            {% if skin_type.name == 'oily' %}
                            <li><i class="fas fa-dot-circle text-success"></i> Nettoyer 2 fois par jour</li>
                            <li><i class="fas fa-dot-circle text-success"></i> Utiliser des produits non-comédogènes</li>
                            <li><i class="fas fa-dot-circle text-success"></i> Appliquer une protection solaire matifiante</li>
                            {% elif skin_type.name == 'dry' %}
                            <li><i class="fas fa-dot-circle text-success"></i> Hydrater matin et soir</li>
                            <li><i class="fas fa-dot-circle text-success"></i> Utiliser des nettoyants doux</li>
                            <li><i class="fas fa-dot-circle text-success"></i> Éviter l'eau trop chaude</li>
                            {% elif skin_type.name == 'sensitive' %}
                            <li><i class="fas fa-dot-circle text-success"></i> Tester les produits avant utilisation</li>
                            <li><i class="fas fa-dot-circle text-success"></i> Choisir des formules hypoallergéniques</li>
                            <li><i class="fas fa-dot-circle text-success"></i> Protéger du soleil et du vent</li>
                            {% else %}
                            <li><i class="fas fa-dot-circle text-success"></i> Maintenir une routine régulière</li>
                            <li><i class="fas fa-dot-circle text-success"></i> Nettoyer et hydrater quotidiennement</li>
                            <li><i class="fas fa-dot-circle text-success"></i> Protéger du soleil</li>
                            {% endif %}
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6><i class="fas fa-times-circle text-danger"></i> À Éviter</h6>
                        <ul class="list-unstyled">
                            {% if skin_type.name == 'oily' %}
                            <li><i class="fas fa-dot-circle text-danger"></i> Sur-nettoyer la peau</li>
                            <li><i class="fas fa-dot-circle text-danger"></i> Produits à base d'alcool</li>
                            <li><i class="fas fa-dot-circle text-danger"></i> Crèmes trop riches</li>
                            {% elif skin_type.name == 'dry' %}
                            <li><i class="fas fa-dot-circle text-danger"></i> Nettoyants agressifs</li>
                            <li><i class="fas fa-dot-circle text-danger"></i> Produits parfumés</li>
                            <li><i class="fas fa-dot-circle text-danger"></i> Exfoliation excessive</li>
                            {% elif skin_type.name == 'sensitive' %}
                            <li><i class="fas fa-dot-circle text-danger"></i> Parfums et colorants</li>
                            <li><i class="fas fa-dot-circle text-danger"></i> Acides forts</li>
                            <li><i class="fas fa-dot-circle text-danger"></i> Changements brusques de routine</li>
                            {% else %}
                            <li><i class="fas fa-dot-circle text-danger"></i> Négliger la protection solaire</li>
                            <li><i class="fas fa-dot-circle text-danger"></i> Produits inadaptés</li>
                            <li><i class="fas fa-dot-circle text-danger"></i> Routine irrégulière</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Back to Analysis -->
    <div class="text-center mt-4 mb-5">
        <a href="{% url 'skin_analysis:form_analysis' %}" class="btn btn-outline-light">
            <i class="fas fa-arrow-left"></i> Nouvelle Analyse
        </a>
        <a href="{% url 'skin_analysis:home' %}" class="btn btn-light ms-2">
            <i class="fas fa-home"></i> Accueil
        </a>
    </div>
</div>
{% endblock %}

"""
Commande Django pour tester le modèle ML entraîné
Usage: python manage.py test_ml_model
"""

import os
import json
import numpy as np
from django.core.management.base import BaseCommand
from django.conf import settings
from skin_analysis.ml_models import SkinTypeClassifier
from skin_analysis.dataset_downloader import dataset_downloader
import cv2
from PIL import Image

class Command(BaseCommand):
    help = 'Teste le modèle ML entraîné sur des échantillons'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dataset-path',
            type=str,
            help='Chemin vers le dataset de test'
        )
        parser.add_argument(
            '--num-samples',
            type=int,
            default=20,
            help='Nombre d\'échantillons à tester'
        )
        parser.add_argument(
            '--show-details',
            action='store_true',
            help='Afficher les détails de chaque prédiction'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🧪 Test du modèle ML'))
        
        # Initialiser le classificateur
        classifier = SkinTypeClassifier()
        
        if classifier.model is None:
            self.stdout.write(self.style.ERROR('❌ Aucun modèle entraîné trouvé'))
            self.stdout.write(self.style.WARNING('💡 Entraînez d\'abord un modèle avec: python manage.py train_ml_model'))
            return
        
        # Déterminer le dataset de test
        dataset_path = options['dataset_path']
        if not dataset_path:
            dataset_path = os.path.join(dataset_downloader.base_path, 'synthetic_demo')
        
        if not os.path.exists(dataset_path):
            self.stdout.write(self.style.ERROR(f'❌ Dataset non trouvé: {dataset_path}'))
            self.stdout.write(self.style.WARNING('💡 Téléchargez un dataset avec: python manage.py download_datasets'))
            return
        
        # Charger les échantillons de test
        test_samples = self.load_test_samples(dataset_path, options['num_samples'])
        
        if not test_samples:
            self.stdout.write(self.style.ERROR('❌ Aucun échantillon de test trouvé'))
            return
        
        # Tester le modèle
        results = self.test_model(classifier, test_samples, options['show_details'])
        
        # Afficher les résultats
        self.display_results(results)

    def load_test_samples(self, dataset_path, num_samples):
        """Charge des échantillons de test"""
        annotations_file = os.path.join(dataset_path, 'annotations.json')
        
        if not os.path.exists(annotations_file):
            self.stdout.write(self.style.ERROR(f'❌ Fichier d\'annotations non trouvé'))
            return []
        
        try:
            with open(annotations_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Erreur lecture annotations: {e}'))
            return []
        
        # Sélectionner des échantillons aléatoirement
        import random
        random.shuffle(annotations)
        
        test_samples = []
        for annotation in annotations[:num_samples]:
            image_path = os.path.join(dataset_path, annotation['filename'])
            if os.path.exists(image_path):
                test_samples.append({
                    'image_path': image_path,
                    'true_type': annotation['skin_type'],
                    'filename': annotation['filename']
                })
        
        self.stdout.write(f"📊 Échantillons de test chargés: {len(test_samples)}")
        return test_samples

    def test_model(self, classifier, test_samples, show_details):
        """Teste le modèle sur les échantillons"""
        results = {
            'total': len(test_samples),
            'correct': 0,
            'predictions': [],
            'confusion_matrix': {},
            'accuracy_by_type': {}
        }
        
        skin_types = ['normal', 'oily', 'dry', 'combination', 'sensitive', 'acne_prone']
        
        # Initialiser la matrice de confusion
        for true_type in skin_types:
            results['confusion_matrix'][true_type] = {}
            for pred_type in skin_types:
                results['confusion_matrix'][true_type][pred_type] = 0
        
        self.stdout.write("🔄 Test en cours...")
        
        for i, sample in enumerate(test_samples):
            try:
                # Prédiction
                predicted_type, confidence, analysis_data = classifier.predict(sample['image_path'])
                
                # Vérifier si correct
                is_correct = predicted_type == sample['true_type']
                if is_correct:
                    results['correct'] += 1
                
                # Mettre à jour la matrice de confusion
                results['confusion_matrix'][sample['true_type']][predicted_type] += 1
                
                # Stocker la prédiction
                prediction = {
                    'filename': sample['filename'],
                    'true_type': sample['true_type'],
                    'predicted_type': predicted_type,
                    'confidence': confidence,
                    'is_correct': is_correct,
                    'analysis_data': analysis_data
                }
                results['predictions'].append(prediction)
                
                # Afficher les détails si demandé
                if show_details:
                    status = "✅" if is_correct else "❌"
                    self.stdout.write(
                        f"{status} {sample['filename']}: "
                        f"Vrai={sample['true_type']}, "
                        f"Prédit={predicted_type} ({confidence:.1%})"
                    )
                
                # Progression
                if (i + 1) % 5 == 0:
                    progress = ((i + 1) / len(test_samples)) * 100
                    self.stdout.write(f"📈 Progression: {progress:.1f}%")
                
            except Exception as e:
                self.stdout.write(f"⚠️  Erreur test {sample['filename']}: {e}")
        
        # Calculer la précision par type
        for skin_type in skin_types:
            total_type = sum(results['confusion_matrix'][skin_type].values())
            correct_type = results['confusion_matrix'][skin_type][skin_type]
            
            if total_type > 0:
                accuracy = (correct_type / total_type) * 100
                results['accuracy_by_type'][skin_type] = accuracy
            else:
                results['accuracy_by_type'][skin_type] = 0
        
        return results

    def display_results(self, results):
        """Affiche les résultats du test"""
        overall_accuracy = (results['correct'] / results['total']) * 100
        
        self.stdout.write(self.style.SUCCESS('\n🎯 RÉSULTATS DU TEST'))
        self.stdout.write(f"📊 Précision globale: {overall_accuracy:.1f}% ({results['correct']}/{results['total']})")
        
        # Précision par type de peau
        self.stdout.write("\n📈 Précision par type de peau:")
        for skin_type, accuracy in results['accuracy_by_type'].items():
            if accuracy > 0:
                color = self.style.SUCCESS if accuracy >= 80 else self.style.WARNING if accuracy >= 60 else self.style.ERROR
                self.stdout.write(f"  {color(f'{skin_type}: {accuracy:.1f}%')}")
        
        # Matrice de confusion simplifiée
        self.stdout.write("\n🔍 Matrice de confusion (top erreurs):")
        for true_type, predictions in results['confusion_matrix'].items():
            total = sum(predictions.values())
            if total > 0:
                errors = [(pred_type, count) for pred_type, count in predictions.items() 
                         if pred_type != true_type and count > 0]
                if errors:
                    errors.sort(key=lambda x: x[1], reverse=True)
                    main_error = errors[0]
                    self.stdout.write(f"  {true_type} → {main_error[0]}: {main_error[1]} erreurs")
        
        # Recommandations
        self.stdout.write(self.style.WARNING("\n💡 Recommandations:"))
        if overall_accuracy >= 90:
            self.stdout.write("🎉 Excellent modèle ! Prêt pour la production.")
        elif overall_accuracy >= 75:
            self.stdout.write("👍 Bon modèle. Considérez plus de données d'entraînement.")
        elif overall_accuracy >= 60:
            self.stdout.write("⚠️  Modèle moyen. Plus d'entraînement nécessaire.")
        else:
            self.stdout.write("❌ Modèle faible. Vérifiez les données et l'architecture.")
        
        # Suggestions d'amélioration
        worst_type = min(results['accuracy_by_type'].items(), key=lambda x: x[1])
        if worst_type[1] < 70:
            self.stdout.write(f"🔧 Type le plus problématique: {worst_type[0]} ({worst_type[1]:.1f}%)")
            self.stdout.write("   → Collectez plus de données pour ce type")
            self.stdout.write("   → Utilisez l'augmentation de données")
        
        self.stdout.write(f"\n📁 Modèle testé: {SkinTypeClassifier().model_path}")
        self.stdout.write("🔄 Pour réentraîner: python manage.py train_ml_model --use-real-dataset")

{% extends 'skin_analysis/base.html' %}
{% block title %}Guide Camo - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="text-center mb-5">
        <h2 class="text-white">
          <i class="fas fa-mobile-alt"></i> Guide Camo + Analyse de Peau
        </h2>
        <p class="text-white-50">Comment utiliser Camo avec notre système d'analyse</p>
      </div>

      <!-- Problème Identifié -->
      <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
          <h4><i class="fas fa-exclamation-triangle"></i> Problème Détecté</h4>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <h5>🚫 "Caméra utilisée par une autre application"</h5>
            <p>Cette erreur apparaît car <strong>Camo utilise déjà votre caméra</strong>. Le navigateur ne peut pas accéder à une caméra déjà utilisée.</p>
          </div>
        </div>
      </div>

      <!-- Solutions -->
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h4><i class="fas fa-lightbulb"></i> Solutions Disponibles</h4>
        </div>
        <div class="card-body">
          
          <!-- Solution 1: Upload Photo -->
          <div class="solution-card mb-4 p-3 border rounded">
            <h5 class="text-success">✅ Solution 1 : Upload Photo (Recommandée)</h5>
            <p>Prenez une photo avec Camo et uploadez-la pour analyse.</p>
            
            <div class="row">
              <div class="col-md-6">
                <h6>📱 Étapes :</h6>
                <ol>
                  <li>Ouvrez <strong>Camo</strong> sur iPhone + PC</li>
                  <li>Positionnez votre visage dans le cadre</li>
                  <li>Prenez une <strong>capture d'écran</strong> (Windows + Shift + S)</li>
                  <li>Sauvegardez l'image</li>
                  <li>Sur notre site, cliquez <strong>"Upload Photo"</strong></li>
                  <li>Sélectionnez votre image</li>
                  <li>Cliquez <strong>"Analyser Photo"</strong></li>
                </ol>
              </div>
              <div class="col-md-6">
                <h6>🎯 Avantages :</h6>
                <ul>
                  <li>✅ Fonctionne avec Camo actif</li>
                  <li>✅ Qualité iPhone excellente</li>
                  <li>✅ Contrôle total de l'image</li>
                  <li>✅ Pas de conflit caméra</li>
                  <li>✅ Même précision ML</li>
                </ul>
              </div>
            </div>
            
            <div class="text-center mt-3">
              <a href="{% url 'skin_analysis:camera_test' %}" class="btn btn-success btn-lg">
                <i class="fas fa-upload"></i> Essayer Upload Photo
              </a>
            </div>
          </div>

          <!-- Solution 2: Camo comme Source -->
          <div class="solution-card mb-4 p-3 border rounded">
            <h5 class="text-info">🔄 Solution 2 : Utiliser Camo comme Webcam</h5>
            <p>Configurer le navigateur pour utiliser Camo directement.</p>
            
            <div class="row">
              <div class="col-md-6">
                <h6>⚙️ Configuration :</h6>
                <ol>
                  <li>Assurez-vous que <strong>Camo est connecté</strong></li>
                  <li>Sur notre site, cliquez <strong>"Démarrer Caméra"</strong></li>
                  <li>Dans la popup de permission caméra :</li>
                  <li>Choisissez <strong>"Camo"</strong> ou <strong>"iPhone Camera"</strong></li>
                  <li>Cliquez <strong>"Autoriser"</strong></li>
                </ol>
              </div>
              <div class="col-md-6">
                <h6>⚠️ Limitations :</h6>
                <ul>
                  <li>Peut ne pas fonctionner sur tous les navigateurs</li>
                  <li>Dépend de la configuration Camo</li>
                  <li>Parfois instable</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Solution 3: Fermer Camo -->
          <div class="solution-card mb-4 p-3 border rounded">
            <h5 class="text-secondary">🔄 Solution 3 : Fermer Temporairement Camo</h5>
            <p>Utiliser la caméra intégrée de l'ordinateur.</p>
            
            <div class="row">
              <div class="col-md-6">
                <h6>📱 Étapes :</h6>
                <ol>
                  <li><strong>Fermez Camo</strong> complètement (iPhone + PC)</li>
                  <li>Actualisez la page web</li>
                  <li>Cliquez <strong>"Démarrer Caméra"</strong></li>
                  <li>Autorisez l'accès caméra intégrée</li>
                  <li>Analysez votre peau</li>
                  <li>Relancez Camo après</li>
                </ol>
              </div>
              <div class="col-md-6">
                <h6>⚖️ Compromis :</h6>
                <ul>
                  <li>✅ Fonctionne à coup sûr</li>
                  <li>❌ Qualité caméra PC (souvent inférieure)</li>
                  <li>❌ Doit fermer/rouvrir Camo</li>
                  <li>❌ Interruption workflow</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Conseils Qualité Photo -->
      <div class="card mb-4">
        <div class="card-header bg-info text-white">
          <h4><i class="fas fa-camera"></i> Conseils pour Photo de Qualité</h4>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>💡 Éclairage Optimal :</h6>
              <ul>
                <li>🌞 <strong>Lumière naturelle</strong> (près d'une fenêtre)</li>
                <li>💡 Évitez l'éclairage direct/harsh</li>
                <li>🚫 Pas de contre-jour</li>
                <li>⚖️ Éclairage uniforme sur le visage</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6>📸 Cadrage Parfait :</h6>
              <ul>
                <li>👤 <strong>Visage centré</strong> dans le cadre</li>
                <li>📏 Distance 30-50cm de la caméra</li>
                <li>👁️ Regardez directement la caméra</li>
                <li>😐 Expression neutre (pas de sourire)</li>
                <li>🧴 Peau propre, sans maquillage</li>
              </ul>
            </div>
          </div>
          
          <div class="alert alert-info mt-3">
            <h6><i class="fas fa-star"></i> Astuce Pro avec Camo :</h6>
            <p>Utilisez les <strong>filtres Camo</strong> pour améliorer l'éclairage, mais désactivez les filtres de beauté pour une analyse précise !</p>
          </div>
        </div>
      </div>

      <!-- Dépannage -->
      <div class="card mb-4">
        <div class="card-header bg-danger text-white">
          <h4><i class="fas fa-wrench"></i> Dépannage</h4>
        </div>
        <div class="card-body">
          <div class="accordion" id="troubleshootAccordion">
            
            <div class="accordion-item">
              <h6 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#error1">
                  ❌ "Caméra utilisée par une autre application"
                </button>
              </h6>
              <div id="error1" class="accordion-collapse collapse" data-bs-parent="#troubleshootAccordion">
                <div class="accordion-body">
                  <strong>Cause :</strong> Camo utilise déjà la caméra<br>
                  <strong>Solution :</strong> Utilisez l'upload photo (Solution 1) ou fermez Camo temporairement
                </div>
              </div>
            </div>

            <div class="accordion-item">
              <h6 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#error2">
                  🚫 "Permission refusée"
                </button>
              </h6>
              <div id="error2" class="accordion-collapse collapse" data-bs-parent="#troubleshootAccordion">
                <div class="accordion-body">
                  <strong>Cause :</strong> Navigateur bloque l'accès caméra<br>
                  <strong>Solution :</strong> Cliquez sur l'icône caméra dans la barre d'adresse → Autoriser
                </div>
              </div>
            </div>

            <div class="accordion-item">
              <h6 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#error3">
                  📱 "Camo ne se connecte pas"
                </button>
              </h6>
              <div id="error3" class="accordion-collapse collapse" data-bs-parent="#troubleshootAccordion">
                <div class="accordion-body">
                  <strong>Solutions :</strong>
                  <ul>
                    <li>Vérifiez que iPhone et PC sont sur le même WiFi</li>
                    <li>Redémarrez l'app Camo sur iPhone</li>
                    <li>Redémarrez le logiciel Camo sur PC</li>
                    <li>Utilisez l'upload photo en attendant</li>
                  </ul>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="text-center">
        <a href="{% url 'skin_analysis:camera_test' %}" class="btn btn-success btn-lg">
          <i class="fas fa-upload"></i> Analyser avec Upload Photo
        </a>
        <a href="{% url 'skin_analysis:camera_analysis' %}" class="btn btn-primary btn-lg ms-2">
          <i class="fas fa-camera"></i> Essayer Caméra Directe
        </a>
        <a href="{% url 'skin_analysis:home' %}" class="btn btn-outline-light btn-lg ms-2">
          <i class="fas fa-home"></i> Retour Accueil
        </a>
      </div>
    </div>
  </div>
</div>

<style>
.solution-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.solution-card h5 {
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 10px;
}
</style>
{% endblock %}

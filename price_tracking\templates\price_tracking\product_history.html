{% extends 'skin_analysis/base.html' %}

{% block title %}Historique des Prix - {{ product.name }} - SkinCare Tunisia{% endblock %}

{% block extra_css %}
<style>
    .price-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .price-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .price-trend-up {
        color: #dc3545;
    }
    
    .price-trend-down {
        color: #28a745;
    }
    
    .price-trend-stable {
        color: #6c757d;
    }
    
    .store-badge {
        border-radius: 20px;
        padding: 5px 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .best-price {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }
    
    .sale-badge {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        color: white;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .chart-container {
        position: relative;
        height: 400px;
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <!-- Product Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card price-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            {% if product.image %}
                            <img src="{{ product.image.url }}" class="img-fluid rounded" alt="{{ product.name }}">
                            {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 100px;">
                                <i class="fas fa-image fa-2x text-muted"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-8">
                            <h2>{{ product.name }}</h2>
                            <p class="text-muted mb-2">
                                <i class="fas fa-building"></i> {{ product.brand.name }}
                            </p>
                            <p class="mb-2">{{ product.description }}</p>
                            <div class="mb-2">
                                {% for skin_type in product.suitable_skin_types.all %}
                                <span class="badge bg-primary me-1">{{ skin_type.get_name_display }}</span>
                                {% endfor %}
                            </div>
                            {% if product.volume %}
                            <small class="text-muted">
                                <i class="fas fa-flask"></i> {{ product.volume }}
                            </small>
                            {% endif %}
                        </div>
                        <div class="col-md-2 text-center">
                            {% if user.is_authenticated %}
                            <button class="btn btn-warning mb-2" data-bs-toggle="modal" data-bs-target="#alertModal">
                                <i class="fas fa-bell"></i> Créer Alerte
                            </button>
                            {% endif %}
                            <a href="{% url 'products:detail' product.id %}" class="btn btn-outline-primary">
                                <i class="fas fa-info-circle"></i> Détails
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Price Statistics -->
    {% if stats %}
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card price-card text-center">
                <div class="card-body">
                    <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                    <h5>Prix Moyen</h5>
                    <h3 class="text-success">{{ stats.avg_price|floatformat:3 }} TND</h3>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card price-card text-center">
                <div class="card-body">
                    <i class="fas fa-arrow-down fa-2x text-primary mb-2"></i>
                    <h5>Prix Minimum</h5>
                    <h3 class="text-primary">{{ stats.min_price|floatformat:3 }} TND</h3>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card price-card text-center">
                <div class="card-body">
                    <i class="fas fa-arrow-up fa-2x text-danger mb-2"></i>
                    <h5>Prix Maximum</h5>
                    <h3 class="text-danger">{{ stats.max_price|floatformat:3 }} TND</h3>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Price Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-chart-area"></i> Évolution des Prix (30 derniers jours)</h5>
                <canvas id="priceChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Current Prices by Store -->
    <div class="row">
        <div class="col-12">
            <div class="card price-card">
                <div class="card-header">
                    <h5><i class="fas fa-store"></i> Prix Actuels par Magasin</h5>
                </div>
                <div class="card-body">
                    {% if price_history %}
                    <div class="row">
                        {% for price in price_history %}
                        {% if forloop.counter0 < 10 %}
                        <div class="col-lg-6 mb-3">
                            <div class="card h-100 {% if price.price == stats.min_price %}border-success{% endif %}">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6>{{ price.store.name }}</h6>
                                            <div class="mb-2">
                                                {% if price.price == stats.min_price %}
                                                <span class="store-badge best-price">
                                                    <i class="fas fa-crown"></i> Meilleur Prix
                                                </span>
                                                {% endif %}
                                                {% if price.is_on_sale %}
                                                <span class="store-badge sale-badge">
                                                    <i class="fas fa-fire"></i> Promotion
                                                </span>
                                                {% endif %}
                                            </div>
                                            {% if price.store.location %}
                                            <small class="text-muted">
                                                <i class="fas fa-map-marker-alt"></i> {{ price.store.location }}
                                            </small>
                                            {% endif %}
                                        </div>
                                        <div class="text-end">
                                            {% if price.is_on_sale and price.original_price %}
                                            <div>
                                                <small class="text-muted text-decoration-line-through">
                                                    {{ price.original_price }} TND
                                                </small>
                                            </div>
                                            <div class="text-danger">
                                                <small>-{{ price.discount_percentage|floatformat:0 }}%</small>
                                            </div>
                                            {% endif %}
                                            <h5 class="mb-0 {% if price.is_on_sale %}text-danger{% else %}text-success{% endif %}">
                                                {{ price.price|floatformat:3 }} TND
                                            </h5>
                                            <small class="text-muted">
                                                {{ price.date_recorded|date:"d/m H:i" }}
                                            </small>
                                        </div>
                                    </div>
                                    
                                    {% if price.product_url %}
                                    <div class="mt-2">
                                        <a href="{{ price.product_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-external-link-alt"></i> Voir sur le site
                                        </a>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h6>Aucun prix disponible</h6>
                        <p class="text-muted">Les prix pour ce produit ne sont pas encore disponibles.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Price Alert Modal -->
{% if user.is_authenticated %}
<div class="modal fade" id="alertModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-bell"></i> Créer une Alerte de Prix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'price_tracking:create_alert' product.id %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <p>Recevez une notification quand le prix de <strong>{{ product.name }}</strong> descend en dessous de votre prix cible.</p>
                    
                    <div class="mb-3">
                        <label for="target_price" class="form-label">Prix cible (TND)</label>
                        <input type="number" class="form-control" id="target_price" name="target_price" 
                               step="0.001" min="0" required 
                               {% if stats.min_price %}value="{{ stats.min_price|floatformat:3 }}"{% endif %}>
                        <small class="form-text text-muted">
                            Prix minimum actuel: {{ stats.min_price|floatformat:3 }} TND
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-bell"></i> Créer l'Alerte
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Price Chart
    const ctx = document.getElementById('priceChart').getContext('2d');
    const storesData = {{ stores_prices|safe }};
    
    const datasets = [];
    const colors = ['#667eea', '#764ba2', '#28a745', '#dc3545', '#ffc107', '#17a2b8', '#6f42c1'];
    let colorIndex = 0;
    
    for (const [storeName, prices] of Object.entries(storesData)) {
        const data = prices.map(p => ({
            x: p.date,
            y: p.price
        }));
        
        datasets.push({
            label: storeName,
            data: data,
            borderColor: colors[colorIndex % colors.length],
            backgroundColor: colors[colorIndex % colors.length] + '20',
            tension: 0.4,
            fill: false
        });
        colorIndex++;
    }
    
    new Chart(ctx, {
        type: 'line',
        data: {
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'day',
                        displayFormats: {
                            day: 'DD/MM'
                        }
                    },
                    title: {
                        display: true,
                        text: 'Date'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Prix (TND)'
                    },
                    beginAtZero: false
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y.toFixed(3) + ' TND';
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
});
</script>
{% endblock %}

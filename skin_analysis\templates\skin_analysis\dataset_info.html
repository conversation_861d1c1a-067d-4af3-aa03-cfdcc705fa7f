{% extends 'skin_analysis/base.html' %}
{% block title %}Dataset et Méthodologie - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="text-center mb-5">
        <h2 class="text-white">
          <i class="fas fa-database"></i> Dataset et Méthodologie d'Analyse
        </h2>
        <p class="text-white-50">Transparence totale sur notre approche d'analyse de peau</p>
      </div>

      <!-- État Actuel du Dataset -->
      <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
          <h4><i class="fas fa-info-circle"></i> État Actuel du Dataset</h4>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <h5>🚧 Phase de Développement</h5>
            <p><strong>Actuellement, nous n'utilisons PAS encore de dataset d'images réelles.</strong></p>
            <p>Notre système utilise des <strong>algorithmes de vision par ordinateur avancés</strong> pour analyser les caractéristiques de votre peau en temps réel.</p>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <h6>✅ Ce qui fonctionne maintenant :</h6>
              <ul>
                <li>Analyse de texture (variance Laplacienne)</li>
                <li>Détection d'imperfections (contours)</li>
                <li>Analyse de couleur et brillance</li>
                <li>Comparaison zone T vs joues</li>
                <li>Détection de pores dilatés</li>
                <li>Analyse HSV (teinte, saturation, valeur)</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6>🔄 En cours de développement :</h6>
              <ul>
                <li>Collecte de dataset avec consentement</li>
                <li>Annotations par dermatologues</li>
                <li>Modèle CNN EfficientNet</li>
                <li>Apprentissage automatique</li>
                <li>Validation clinique</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Méthodologie Actuelle -->
      <div class="card mb-4">
        <div class="card-header bg-info text-white">
          <h4><i class="fas fa-cogs"></i> Méthodologie Actuelle (Algorithmes CV)</h4>
        </div>
        <div class="card-body">
          <h5>🔬 Analyse Multi-Critères</h5>
          <p>Notre algorithme analyse <strong>6 types de peau</strong> avec des critères précis :</p>
          
          <div class="accordion" id="methodologyAccordion">
            <!-- Peau Acnéique -->
            <div class="accordion-item">
              <h6 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#acne">
                  🔴 Peau à Tendance Acnéique
                </button>
              </h6>
              <div id="acne" class="accordion-collapse collapse" data-bs-parent="#methodologyAccordion">
                <div class="accordion-body">
                  <strong>Critères détectés :</strong>
                  <ul>
                    <li>Petites imperfections (5-50 pixels) : +1 à +4 points</li>
                    <li>Grandes imperfections (50-200 pixels) : +2 à +3 points</li>
                    <li>Variance de texture élevée (>1000) : +2 à +3 points</li>
                    <li>Densité de pores élevée (>15%) : +2 points</li>
                  </ul>
                  <strong>Seuil de détection :</strong> Score ≥ 4 = 85%+ confiance
                </div>
              </div>
            </div>

            <!-- Peau Grasse -->
            <div class="accordion-item">
              <h6 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#oily">
                  💧 Peau Grasse
                </button>
              </h6>
              <div id="oily" class="accordion-collapse collapse" data-bs-parent="#methodologyAccordion">
                <div class="accordion-body">
                  <strong>Critères détectés :</strong>
                  <ul>
                    <li>Ratio de zones brillantes >35% : +3 points</li>
                    <li>Luminosité moyenne >150 : +2 points</li>
                    <li>Densité de pores >10% : +1 point</li>
                    <li>Zone T plus brillante que joues : +1 point</li>
                  </ul>
                  <strong>Seuil de détection :</strong> Score ≥ 3 = 70%+ confiance
                </div>
              </div>
            </div>

            <!-- Peau Sèche -->
            <div class="accordion-item">
              <h6 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#dry">
                  🏜️ Peau Sèche
                </button>
              </h6>
              <div id="dry" class="accordion-collapse collapse" data-bs-parent="#methodologyAccordion">
                <div class="accordion-body">
                  <strong>Critères détectés :</strong>
                  <ul>
                    <li>Ratio de zones brillantes <5% : +3 points</li>
                    <li>Luminosité faible <90 : +2 points</li>
                    <li>Saturation faible <40 : +2 points</li>
                    <li>Texture très lisse (variance <300) : +1 point</li>
                  </ul>
                  <strong>Seuil de détection :</strong> Score ≥ 3 = 70%+ confiance
                </div>
              </div>
            </div>

            <!-- Peau Mixte -->
            <div class="accordion-item">
              <h6 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#combination">
                  🎭 Peau Mixte
                </button>
              </h6>
              <div id="combination" class="accordion-collapse collapse" data-bs-parent="#methodologyAccordion">
                <div class="accordion-body">
                  <strong>Critères détectés :</strong>
                  <ul>
                    <li>Différence zone T/joues >20 : +3 points</li>
                    <li>Zone T significativement plus brillante : +2 points</li>
                    <li>Variation de luminosité importante : +1 point</li>
                  </ul>
                  <strong>Seuil de détection :</strong> Score ≥ 3 = 80%+ confiance
                </div>
              </div>
            </div>

            <!-- Peau Sensible -->
            <div class="accordion-item">
              <h6 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sensitive">
                  🌹 Peau Sensible
                </button>
              </h6>
              <div id="sensitive" class="accordion-collapse collapse" data-bs-parent="#methodologyAccordion">
                <div class="accordion-body">
                  <strong>Critères détectés :</strong>
                  <ul>
                    <li>Dominance rouge >1.3 : +3 points</li>
                    <li>Variation de rouge élevée >35 : +2 points</li>
                    <li>Rougeurs visibles : +1 point</li>
                  </ul>
                  <strong>Seuil de détection :</strong> Score ≥ 3 = 75%+ confiance
                </div>
              </div>
            </div>

            <!-- Peau Normale -->
            <div class="accordion-item">
              <h6 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#normal">
                  ✨ Peau Normale
                </button>
              </h6>
              <div id="normal" class="accordion-collapse collapse" data-bs-parent="#methodologyAccordion">
                <div class="accordion-body">
                  <strong>Critères détectés :</strong>
                  <ul>
                    <li>Brillance équilibrée (10-20%) : +2 points</li>
                    <li>Luminosité modérée (110-140) : +2 points</li>
                    <li>Texture équilibrée (500-1000) : +1 point</li>
                    <li>Peu d'imperfections (≤3) : +2 points</li>
                  </ul>
                  <strong>Seuil de détection :</strong> Par défaut si autres scores faibles
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Calcul des Pourcentages -->
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h4><i class="fas fa-calculator"></i> Calcul des Pourcentages</h4>
        </div>
        <div class="card-body">
          <h5>🎯 Méthode de Calcul Précise</h5>
          <ol>
            <li><strong>Scores bruts :</strong> Chaque critère ajoute des points (0-10)</li>
            <li><strong>Normalisation :</strong> Conversion en pourcentages sur 100%</li>
            <li><strong>Minimum garanti :</strong> Chaque type a minimum 5%</li>
            <li><strong>Maximum limité :</strong> Aucun type ne peut dépasser 85%</li>
            <li><strong>Redistribution :</strong> Ajustement proportionnel pour totaliser 100%</li>
          </ol>
          
          <div class="alert alert-info">
            <strong>Exemple :</strong> Si vous avez 8 points acné, 3 points gras, 1 point sec :<br>
            • Total = 12 points<br>
            • Acné = (8/12) × 70% + 5% = <strong>51.7%</strong><br>
            • Gras = (3/12) × 70% + 5% = <strong>22.5%</strong><br>
            • Autres types = répartition du reste
          </div>
        </div>
      </div>

      <!-- Roadmap Dataset ML -->
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h4><i class="fas fa-road"></i> Roadmap : Vers le Machine Learning</h4>
        </div>
        <div class="card-body">
          <h5>🚀 Phases de Développement</h5>
          
          <div class="timeline">
            <div class="row">
              <div class="col-md-6">
                <h6>📅 Phase 1 (Actuelle) - Algorithmes CV</h6>
                <ul>
                  <li>✅ Analyse temps réel</li>
                  <li>✅ 6 types de peau détectés</li>
                  <li>✅ Pourcentages précis</li>
                  <li>✅ Collecte de feedback utilisateur</li>
                </ul>
              </div>
              <div class="col-md-6">
                <h6>📅 Phase 2 (Q3 2024) - Dataset Building</h6>
                <ul>
                  <li>🔄 Collecte 10,000+ images</li>
                  <li>🔄 Annotations dermatologues</li>
                  <li>🔄 Validation clinique</li>
                  <li>🔄 Données démographiques Tunisie</li>
                </ul>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-md-6">
                <h6>📅 Phase 3 (Q4 2024) - ML Training</h6>
                <ul>
                  <li>⏳ Modèle EfficientNet B3</li>
                  <li>⏳ Transfer Learning</li>
                  <li>⏳ Augmentation de données</li>
                  <li>⏳ Validation croisée</li>
                </ul>
              </div>
              <div class="col-md-6">
                <h6>📅 Phase 4 (2025) - Production ML</h6>
                <ul>
                  <li>⏳ Précision >95%</li>
                  <li>⏳ Temps réel <1s</li>
                  <li>⏳ API pour dermatologues</li>
                  <li>⏳ Certification médicale</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Transparence -->
      <div class="card">
        <div class="card-header bg-dark text-white">
          <h4><i class="fas fa-eye"></i> Transparence et Éthique</h4>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>🔒 Confidentialité</h6>
              <ul>
                <li>Images analysées localement</li>
                <li>Pas de stockage sans consentement</li>
                <li>Données anonymisées</li>
                <li>Conformité RGPD</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6>⚖️ Limitations Actuelles</h6>
              <ul>
                <li>Algorithmes, pas ML supervisé</li>
                <li>Précision ~70-80% (vs 95%+ visé)</li>
                <li>Sensible à l'éclairage</li>
                <li>Nécessite validation dermatologique</li>
              </ul>
            </div>
          </div>
          
          <div class="alert alert-warning mt-3">
            <strong>⚠️ Avertissement Médical :</strong> Cette analyse est à des fins informatives uniquement. 
            Pour tout problème de peau persistant, consultez un dermatologue qualifié.
          </div>
        </div>
      </div>

      <div class="text-center mt-4">
        <a href="{% url 'skin_analysis:camera_test' %}" class="btn btn-primary btn-lg">
          <i class="fas fa-camera"></i> Tester l'Analyse
        </a>
        <a href="{% url 'skin_analysis:home' %}" class="btn btn-outline-light btn-lg ms-2">
          <i class="fas fa-home"></i> Retour Accueil
        </a>
      </div>
    </div>
  </div>
</div>
{% endblock %}

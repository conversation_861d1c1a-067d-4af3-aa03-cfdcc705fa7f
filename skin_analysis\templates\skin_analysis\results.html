{% extends 'skin_analysis/base.html' %}

{% block title %}Résultats d'Analyse - SkinCare Tunisia{% endblock %}

{% block extra_css %}
<style>
    .result-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        margin-bottom: 30px;
    }
    
    .skin-type-badge {
        font-size: 2rem;
        padding: 15px 30px;
        border-radius: 50px;
        background: rgba(255, 255, 255, 0.2);
        display: inline-block;
        margin: 20px 0;
    }
    
    .confidence-meter {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        height: 15px;
        overflow: hidden;
        margin: 15px 0;
    }
    
    .confidence-fill {
        background: white;
        height: 100%;
        border-radius: 10px;
        transition: width 1s ease;
    }
    
    .recommendation-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .recommendation-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .product-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 10px;
    }
    
    .price-tag {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: bold;
        display: inline-block;
        margin-top: 10px;
    }
    
    .care-tips {
        background: rgba(102, 126, 234, 0.1);
        border-left: 4px solid #667eea;
        border-radius: 0 10px 10px 0;
        padding: 20px;
        margin: 20px 0;
    }
    
    .ingredient-tag {
        background: #e9ecef;
        color: #495057;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.85rem;
        margin: 2px;
        display: inline-block;
    }
    
    .ingredient-good {
        background: #d4edda;
        color: #155724;
    }
    
    .ingredient-avoid {
        background: #f8d7da;
        color: #721c24;
    }
    
    .analysis-details {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin: 20px 0;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .method-badge {
        background: #667eea;
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <!-- Results Header -->
    <div class="result-header">
        <h1><i class="fas fa-microscope"></i> Résultats de Votre Analyse</h1>
        
        {% if analysis.detected_skin_type %}
        <div class="skin-type-badge">
            {{ analysis.detected_skin_type.get_name_display }}
        </div>
        
        {% if analysis.confidence_score %}
        <div class="mt-3">
            <h5>Niveau de confiance</h5>
            <div class="confidence-meter">
                <div class="confidence-fill" style="width: {{ analysis.confidence_score|floatformat:0|add:'%' }}"></div>
            </div>
            <span class="h4">{{ analysis.confidence_score|floatformat:0 }}%</span>
        </div>
        {% endif %}
        
        <div class="mt-4">
            <span class="method-badge">
                {% if analysis.method == 'form' %}
                    <i class="fas fa-clipboard-list"></i> Analyse par Formulaire
                {% elif analysis.method == 'camera' %}
                    <i class="fas fa-camera"></i> Analyse par Caméra
                {% else %}
                    <i class="fas fa-upload"></i> Analyse par Image
                {% endif %}
            </span>
        </div>
        {% else %}
        <div class="skin-type-badge">
            <i class="fas fa-question-circle"></i> Type non déterminé
        </div>
        <p>Nous n'avons pas pu déterminer votre type de peau avec certitude. Essayez une nouvelle analyse.</p>
        {% endif %}
    </div>
    
    {% if analysis.detected_skin_type %}
    <!-- Skin Type Description -->
    <div class="analysis-details">
        <h3><i class="fas fa-info-circle text-primary"></i> À Propos de Votre Type de Peau</h3>
        <p class="lead">{{ analysis.detected_skin_type.description }}</p>
        
        {% if analysis.detected_skin_type.characteristics %}
        <h5 class="mt-4">Caractéristiques</h5>
        <div class="row">
            {% for key, value in analysis.detected_skin_type.characteristics.items %}
            <div class="col-md-6 mb-2">
                <strong>{{ key|title }}:</strong> {{ value }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
    
    <!-- Care Tips -->
    <div class="care-tips">
        <h4><i class="fas fa-lightbulb text-warning"></i> Conseils de Soins</h4>
        <p>{{ analysis.detected_skin_type.care_tips }}</p>
        
        {% if care_recommendations %}
        <div class="row mt-4">
            {% if care_recommendations.routine %}
            <div class="col-md-6">
                <h6><i class="fas fa-list-ol"></i> Routine Recommandée</h6>
                <ol>
                    {% for step in care_recommendations.routine %}
                    <li>{{ step }}</li>
                    {% endfor %}
                </ol>
            </div>
            {% endif %}
            
            <div class="col-md-6">
                {% if care_recommendations.ingredients_to_look_for %}
                <h6><i class="fas fa-check-circle text-success"></i> Ingrédients Bénéfiques</h6>
                <div class="mb-3">
                    {% for ingredient in care_recommendations.ingredients_to_look_for %}
                    <span class="ingredient-tag ingredient-good">{{ ingredient }}</span>
                    {% endfor %}
                </div>
                {% endif %}
                
                {% if care_recommendations.ingredients_to_avoid %}
                <h6><i class="fas fa-times-circle text-danger"></i> Ingrédients à Éviter</h6>
                <div>
                    {% for ingredient in care_recommendations.ingredients_to_avoid %}
                    <span class="ingredient-tag ingredient-avoid">{{ ingredient }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- Product Recommendations -->
    {% if recommendations %}
    <div class="mt-5">
        <h3 class="text-center mb-4">
            <i class="fas fa-shopping-bag text-primary"></i> 
            Produits Recommandés pour Vous
        </h3>
        
        <div class="row">
            {% for rec in recommendations %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card recommendation-card">
                    {% if rec.product.image %}
                    <img src="{{ rec.product.image.url }}" class="product-image" alt="{{ rec.product.name }}">
                    {% else %}
                    <div class="product-image d-flex align-items-center justify-content-center bg-light">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                    {% endif %}
                    
                    <div class="card-body">
                        <h6 class="card-title">{{ rec.product.name }}</h6>
                        <p class="text-muted small">{{ rec.product.brand.name }}</p>
                        <p class="card-text small">{{ rec.reason }}</p>
                        
                        {% if rec.product.get_current_price %}
                        <div class="price-tag">
                            {{ rec.product.get_current_price }} TND
                        </div>
                        {% endif %}
                        
                        <div class="mt-3">
                            <a href="{% url 'products:detail' rec.product.id %}" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i> Voir Détails
                            </a>
                            <a href="{% url 'price_tracking:product_history' rec.product.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-chart-line"></i> Prix
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-4">
            <a href="{% url 'products:recommendations' analysis.detected_skin_type.name %}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus"></i> Voir Plus de Recommandations
            </a>
        </div>
    </div>
    {% endif %}
    {% endif %}
    
    <!-- Action Buttons -->
    <div class="text-center mt-5 mb-5">
        <a href="{% url 'skin_analysis:form_analysis' %}" class="btn btn-outline-primary btn-lg me-3">
            <i class="fas fa-redo"></i> Nouvelle Analyse
        </a>
        
        {% if user.is_authenticated %}
        <a href="{% url 'skin_analysis:profile' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-user"></i> Mon Profil
        </a>
        {% else %}
        <a href="{% url 'skin_analysis:register' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-user-plus"></i> Créer un Compte
        </a>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate confidence bar
    const confidenceFill = document.querySelector('.confidence-fill');
    if (confidenceFill) {
        setTimeout(() => {
            confidenceFill.style.width = '{{ analysis.confidence_score|floatformat:0 }}%';
        }, 500);
    }
});
</script>
{% endblock %}

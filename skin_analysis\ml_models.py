"""
Modèles de Machine Learning avancés pour l'analyse de peau
Utilise TensorFlow/Keras avec EfficientNet et techniques de deep learning
"""

import os
import numpy as np
import cv2
from PIL import Image
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.applications import EfficientNetB0, EfficientNetB3
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import json
from django.conf import settings

class SkinTypeClassifier:
    """Classificateur avancé de type de peau utilisant EfficientNet"""
    
    def __init__(self, model_path=None):
        self.model = None
        self.input_size = (224, 224)  # Taille optimale pour EfficientNet
        self.skin_types = ['normal', 'oily', 'dry', 'combination', 'sensitive', 'acne_prone']
        self.model_path = model_path or os.path.join(settings.BASE_DIR, 'ml_models', 'skin_classifier.h5')
        
        # Charger le modèle pré-entraîné s'il existe
        if os.path.exists(self.model_path):
            self.load_model()
        else:
            print("Modèle non trouvé. Utilisation du modèle de base.")
            self.create_base_model()
    
    def create_advanced_model(self):
        """Crée un modèle EfficientNet avancé avec attention"""
        
        # Base EfficientNet pré-entraînée
        base_model = EfficientNetB3(
            weights='imagenet',
            include_top=False,
            input_shape=(*self.input_size, 3)
        )
        
        # Geler les premières couches
        base_model.trainable = False
        
        # Architecture avancée avec attention
        inputs = keras.Input(shape=(*self.input_size, 3))
        
        # Préprocessing intégré
        x = keras.applications.efficientnet.preprocess_input(inputs)
        
        # Feature extraction
        x = base_model(x, training=False)
        
        # Attention mechanism (Squeeze-and-Excitation)
        x = self.squeeze_excitation_block(x, ratio=16)
        
        # Global pooling avec attention spatiale
        x = layers.GlobalAveragePooling2D()(x)
        
        # Dropout pour régularisation
        x = layers.Dropout(0.3)(x)
        
        # Couches denses avec batch normalization
        x = layers.Dense(512, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.2)(x)
        
        x = layers.Dense(256, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.1)(x)
        
        # Sortie avec probabilités
        outputs = layers.Dense(len(self.skin_types), activation='softmax', name='skin_type')(x)
        
        model = keras.Model(inputs, outputs)
        
        # Compilation avec optimiseur adaptatif
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        self.model = model
        return model
    
    def squeeze_excitation_block(self, input_tensor, ratio=16):
        """Implémente le mécanisme d'attention Squeeze-and-Excitation"""
        channel_axis = -1
        filters = input_tensor.shape[channel_axis]
        
        # Squeeze
        se = layers.GlobalAveragePooling2D()(input_tensor)
        se = layers.Reshape((1, 1, filters))(se)
        
        # Excitation
        se = layers.Dense(filters // ratio, activation='relu')(se)
        se = layers.Dense(filters, activation='sigmoid')(se)
        
        # Scale
        x = layers.Multiply()([input_tensor, se])
        return x
    
    def create_base_model(self):
        """Crée un modèle de base plus simple pour démarrer"""
        inputs = keras.Input(shape=(*self.input_size, 3))
        
        # Utilisation d'EfficientNetB0 plus léger
        base_model = EfficientNetB0(
            weights='imagenet',
            include_top=False,
            input_shape=(*self.input_size, 3)
        )
        base_model.trainable = False
        
        x = keras.applications.efficientnet.preprocess_input(inputs)
        x = base_model(x, training=False)
        x = layers.GlobalAveragePooling2D()(x)
        x = layers.Dropout(0.2)(x)
        x = layers.Dense(128, activation='relu')(x)
        outputs = layers.Dense(len(self.skin_types), activation='softmax')(x)
        
        self.model = keras.Model(inputs, outputs)
        self.model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
    
    def preprocess_image(self, image_path):
        """Préprocessing avancé de l'image"""
        try:
            # Charger l'image
            img = cv2.imread(image_path)
            if img is None:
                raise ValueError("Impossible de charger l'image")
            
            # Convertir en RGB
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Détection de visage pour crop intelligent
            face_region = self.detect_and_crop_face(img_rgb)
            if face_region is None:
                # Fallback : utiliser la région centrale
                h, w = img_rgb.shape[:2]
                center_y, center_x = h // 2, w // 2
                size = min(h, w) // 2
                face_region = img_rgb[
                    max(0, center_y - size):min(h, center_y + size),
                    max(0, center_x - size):min(w, center_x + size)
                ]
            
            # Correction d'éclairage
            face_region = self.correct_lighting(face_region)
            
            # Réduction du bruit
            face_region = cv2.bilateralFilter(face_region, 9, 75, 75)
            
            # Redimensionner pour le modèle
            face_region = cv2.resize(face_region, self.input_size)
            
            # Normalisation
            face_region = face_region.astype(np.float32) / 255.0
            
            return np.expand_dims(face_region, axis=0)
            
        except Exception as e:
            print(f"Erreur preprocessing: {e}")
            # Retourner une image par défaut
            return np.zeros((1, *self.input_size, 3), dtype=np.float32)
    
    def detect_and_crop_face(self, img):
        """Détection de visage avec OpenCV"""
        try:
            # Utiliser le détecteur de visage Haar Cascade
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(faces) > 0:
                # Prendre le plus grand visage détecté
                x, y, w, h = max(faces, key=lambda f: f[2] * f[3])
                
                # Agrandir la région pour inclure plus de peau
                margin = int(min(w, h) * 0.2)
                x = max(0, x - margin)
                y = max(0, y - margin)
                w = min(img.shape[1] - x, w + 2 * margin)
                h = min(img.shape[0] - y, h + 2 * margin)
                
                return img[y:y+h, x:x+w]
            
            return None
            
        except Exception as e:
            print(f"Erreur détection visage: {e}")
            return None
    
    def correct_lighting(self, img):
        """Correction d'éclairage pour uniformiser"""
        try:
            # Conversion en LAB pour séparer luminance et couleur
            lab = cv2.cvtColor(img, cv2.COLOR_RGB2LAB)
            l, a, b = cv2.split(lab)
            
            # Égalisation adaptative de l'histogramme sur le canal L
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
            
            # Reconstituer l'image
            lab = cv2.merge([l, a, b])
            img_corrected = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
            
            return img_corrected
            
        except Exception as e:
            print(f"Erreur correction éclairage: {e}")
            return img
    
    def predict(self, image_path, include_questionnaire=None):
        """Prédiction avec le modèle avancé"""
        if self.model is None:
            return self.fallback_prediction(image_path)
        
        try:
            # Préprocessing
            processed_img = self.preprocess_image(image_path)
            
            # Prédiction
            predictions = self.model.predict(processed_img, verbose=0)
            probabilities = predictions[0]
            
            # Trouver le type de peau avec la plus haute probabilité
            predicted_idx = np.argmax(probabilities)
            predicted_type = self.skin_types[predicted_idx]
            confidence = float(probabilities[predicted_idx])
            
            # Fusion avec questionnaire si disponible
            if include_questionnaire:
                confidence = self.fuse_with_questionnaire(
                    predicted_type, confidence, include_questionnaire
                )
            
            # Données détaillées
            analysis_data = {
                'probabilities': {
                    skin_type: float(prob) 
                    for skin_type, prob in zip(self.skin_types, probabilities)
                },
                'top_3_predictions': self.get_top_predictions(probabilities),
                'model_used': 'EfficientNet_Advanced',
                'preprocessing_applied': True,
                'face_detected': True
            }
            
            return predicted_type, confidence, analysis_data
            
        except Exception as e:
            print(f"Erreur prédiction ML: {e}")
            return self.fallback_prediction(image_path)
    
    def get_top_predictions(self, probabilities):
        """Retourne les 3 meilleures prédictions"""
        top_indices = np.argsort(probabilities)[-3:][::-1]
        return [
            {
                'skin_type': self.skin_types[idx],
                'probability': float(probabilities[idx])
            }
            for idx in top_indices
        ]
    
    def fuse_with_questionnaire(self, predicted_type, confidence, questionnaire_data):
        """Fusion des résultats image + questionnaire"""
        # Ajustement basé sur les réponses du questionnaire
        adjustments = {
            'oily': 0.1 if questionnaire_data.get('oily_symptoms') else -0.05,
            'dry': 0.1 if questionnaire_data.get('dry_symptoms') else -0.05,
            'acne_prone': 0.15 if questionnaire_data.get('acne_symptoms') else -0.1,
            'sensitive': 0.1 if questionnaire_data.get('sensitive_symptoms') else -0.05
        }
        
        adjustment = adjustments.get(predicted_type, 0)
        return min(0.98, max(0.3, confidence + adjustment))
    
    def fallback_prediction(self, image_path):
        """Méthode de fallback si le modèle ML n'est pas disponible"""
        from .utils import analyze_image_basic
        return analyze_image_basic(image_path)
    
    def load_model(self):
        """Charge un modèle pré-entraîné"""
        try:
            self.model = keras.models.load_model(self.model_path)
            print("Modèle ML chargé avec succès")
        except Exception as e:
            print(f"Erreur chargement modèle: {e}")
            self.create_base_model()
    
    def save_model(self):
        """Sauvegarde le modèle"""
        if self.model:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            self.model.save(self.model_path)
            print(f"Modèle sauvegardé: {self.model_path}")

# Instance globale du classificateur
skin_classifier = SkinTypeClassifier()

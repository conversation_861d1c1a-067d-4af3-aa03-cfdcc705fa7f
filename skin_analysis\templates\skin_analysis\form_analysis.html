{% extends 'skin_analysis/base.html' %}

{% block title %}Analyse par Formulaire - SkinCare Tunisia{% endblock %}

{% block extra_css %}
<style>
    .question-card {
        transition: all 0.3s ease;
        border-left: 4px solid #667eea;
    }
    
    .question-card:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }
    
    .progress-bar {
        background: linear-gradient(45deg, #667eea, #764ba2);
    }
    
    .image-upload-area {
        border: 2px dashed #667eea;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .image-upload-area:hover {
        border-color: #764ba2;
        background-color: rgba(102, 126, 234, 0.05);
    }
    
    .image-upload-area.dragover {
        border-color: #764ba2;
        background-color: rgba(102, 126, 234, 0.1);
    }
    
    .preview-image {
        max-width: 200px;
        max-height: 200px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white text-center">
                    <h3><i class="fas fa-clipboard-list"></i> Analyse de Votre Type de Peau</h3>
                    <p class="mb-0">Répondez aux questions suivantes pour une analyse personnalisée</p>
                </div>
                
                <div class="card-body p-4">
                    <!-- Progress Bar -->
                    <div class="mb-4">
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
                        </div>
                        <small class="text-muted">Question <span id="currentQuestion">1</span> sur <span id="totalQuestions">{{ questions.count }}</span></small>
                    </div>
                    
                    <form method="post" enctype="multipart/form-data" id="analysisForm">
                        {% csrf_token %}
                        
                        <!-- Image Upload Section -->
                        <div class="mb-5">
                            <h5 class="mb-3"><i class="fas fa-camera"></i> Photo de Votre Visage (Optionnel)</h5>
                            <div class="image-upload-area" id="imageUploadArea">
                                <div id="uploadPrompt">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h6>Cliquez ou glissez une photo de votre visage</h6>
                                    <p class="text-muted">Formats acceptés: JPG, PNG, WEBP (Max: 5MB)</p>
                                    <input type="file" name="image" id="imageInput" accept="image/*" style="display: none;">
                                </div>
                                <div id="imagePreview" style="display: none;">
                                    <img id="previewImg" class="preview-image mb-3" alt="Aperçu">
                                    <br>
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeImage">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Questions Section -->
                        <div id="questionsContainer">
                            {% for question in questions %}
                            <div class="question-card card mb-4" data-question="{{ forloop.counter }}">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary me-2">{{ forloop.counter }}</span>
                                        {{ question.question_text }}
                                    </h6>
                                    
                                    {% if question.question_type == 'single_choice' %}
                                        <div class="mt-3">
                                            {% for choice in question.choices %}
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" 
                                                       name="question_{{ question.id }}" 
                                                       id="q{{ question.id }}_{{ forloop.counter }}"
                                                       value="{{ choice }}" required>
                                                <label class="form-check-label" for="q{{ question.id }}_{{ forloop.counter }}">
                                                    {{ choice }}
                                                </label>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    
                                    {% elif question.question_type == 'multiple_choice' %}
                                        <div class="mt-3">
                                            {% for choice in question.choices %}
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="question_{{ question.id }}" 
                                                       id="q{{ question.id }}_{{ forloop.counter }}"
                                                       value="{{ choice }}">
                                                <label class="form-check-label" for="q{{ question.id }}_{{ forloop.counter }}">
                                                    {{ choice }}
                                                </label>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    
                                    {% elif question.question_type == 'scale' %}
                                        <div class="mt-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">Pas du tout</small>
                                                <input type="range" class="form-range" 
                                                       name="question_{{ question.id }}" 
                                                       min="1" max="5" value="3" 
                                                       id="q{{ question.id }}_range">
                                                <small class="text-muted">Beaucoup</small>
                                            </div>
                                            <div class="text-center mt-2">
                                                <span class="badge bg-primary" id="q{{ question.id }}_value">3</span>
                                            </div>
                                        </div>
                                    
                                    {% elif question.question_type == 'text' %}
                                        <div class="mt-3">
                                            <textarea class="form-control" name="question_{{ question.id }}" 
                                                    rows="3" placeholder="Votre réponse..."></textarea>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-search"></i> Analyser Ma Peau
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image upload functionality
    const imageUploadArea = document.getElementById('imageUploadArea');
    const imageInput = document.getElementById('imageInput');
    const uploadPrompt = document.getElementById('uploadPrompt');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const removeImageBtn = document.getElementById('removeImage');
    
    // Click to upload
    imageUploadArea.addEventListener('click', function() {
        imageInput.click();
    });
    
    // File input change
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            displayImage(file);
        }
    });
    
    // Drag and drop
    imageUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        imageUploadArea.classList.add('dragover');
    });
    
    imageUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        imageUploadArea.classList.remove('dragover');
    });
    
    imageUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        imageUploadArea.classList.remove('dragover');
        const file = e.dataTransfer.files[0];
        if (file && file.type.startsWith('image/')) {
            imageInput.files = e.dataTransfer.files;
            displayImage(file);
        }
    });
    
    // Remove image
    removeImageBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        imageInput.value = '';
        uploadPrompt.style.display = 'block';
        imagePreview.style.display = 'none';
    });
    
    function displayImage(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            uploadPrompt.style.display = 'none';
            imagePreview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
    
    // Range input updates
    document.querySelectorAll('input[type="range"]').forEach(function(range) {
        const valueSpan = document.getElementById(range.id + '_value');
        range.addEventListener('input', function() {
            valueSpan.textContent = this.value;
        });
    });
    
    // Progress tracking
    const questions = document.querySelectorAll('.question-card');
    const progressBar = document.getElementById('progressBar');
    const currentQuestionSpan = document.getElementById('currentQuestion');
    const totalQuestions = questions.length;
    
    function updateProgress() {
        let answeredQuestions = 0;
        questions.forEach(function(question, index) {
            const inputs = question.querySelectorAll('input[required]');
            let isAnswered = false;
            
            inputs.forEach(function(input) {
                if (input.type === 'radio' && input.checked) {
                    isAnswered = true;
                }
            });
            
            if (isAnswered) {
                answeredQuestions++;
            }
        });
        
        const progress = (answeredQuestions / totalQuestions) * 100;
        progressBar.style.width = progress + '%';
        currentQuestionSpan.textContent = answeredQuestions + 1;
    }
    
    // Listen for input changes
    document.querySelectorAll('input').forEach(function(input) {
        input.addEventListener('change', updateProgress);
    });
    
    // Form validation
    document.getElementById('analysisForm').addEventListener('submit', function(e) {
        const requiredInputs = document.querySelectorAll('input[required]');
        let allAnswered = true;
        
        // Group radio buttons by name
        const radioGroups = {};
        requiredInputs.forEach(function(input) {
            if (input.type === 'radio') {
                if (!radioGroups[input.name]) {
                    radioGroups[input.name] = [];
                }
                radioGroups[input.name].push(input);
            }
        });
        
        // Check if each radio group has a selection
        for (const groupName in radioGroups) {
            const group = radioGroups[groupName];
            const hasSelection = group.some(radio => radio.checked);
            if (!hasSelection) {
                allAnswered = false;
                break;
            }
        }
        
        if (!allAnswered) {
            e.preventDefault();
            alert('Veuillez répondre à toutes les questions obligatoires.');
            return false;
        }
    });
});
</script>
{% endblock %}

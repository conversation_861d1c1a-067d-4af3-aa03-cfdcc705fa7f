"""
Commande Django pour télécharger des datasets dermatologiques réels
Usage: python manage.py download_datasets --dataset synthetic_demo
"""

import os
from django.core.management.base import BaseCommand
from django.conf import settings
from skin_analysis.dataset_downloader import dataset_downloader

class Command(BaseCommand):
    help = 'Télécharge et prépare des datasets dermatologiques pour l\'entraînement ML'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dataset',
            type=str,
            default='synthetic_demo',
            help='Nom du dataset à télécharger (synthetic_demo, fitzpatrick17k, dermnet, pad_ufes_20)'
        )
        parser.add_argument(
            '--list',
            action='store_true',
            help='Affiche la liste des datasets disponibles'
        )
        parser.add_argument(
            '--prepare-only',
            action='store_true',
            help='Prépare un dataset déjà téléchargé sans le retélécharger'
        )

    def handle(self, *args, **options):
        if options['list']:
            self.list_datasets()
            return
        
        dataset_name = options['dataset']
        
        self.stdout.write(self.style.SUCCESS(f'🚀 Traitement du dataset: {dataset_name}'))
        
        if options['prepare_only']:
            # Préparer un dataset existant
            dataset_path = os.path.join(dataset_downloader.base_path, dataset_name)
            if not os.path.exists(dataset_path):
                self.stdout.write(self.style.ERROR(f'❌ Dataset {dataset_name} non trouvé dans {dataset_path}'))
                return
            
            dataset_info = dataset_downloader.prepare_for_training(dataset_path)
        else:
            # Télécharger et préparer
            dataset_path = dataset_downloader.download_dataset(dataset_name)
            if not dataset_path:
                self.stdout.write(self.style.ERROR(f'❌ Échec du téléchargement de {dataset_name}'))
                return
            
            dataset_info = dataset_downloader.prepare_for_training(dataset_path)
        
        if dataset_info:
            self.stdout.write(self.style.SUCCESS('✅ Dataset prêt pour l\'entraînement!'))
            self.stdout.write(f"📁 Chemin: {dataset_info['dataset_path']}")
            self.stdout.write(f"📊 Total images: {dataset_info['total_images']}")
            
            # Afficher les statistiques détaillées
            self.stdout.write("\n📈 Répartition par type de peau:")
            for skin_type, count in dataset_info['stats'].items():
                percentage = (count / dataset_info['total_images']) * 100
                self.stdout.write(f"  🔸 {skin_type}: {count} images ({percentage:.1f}%)")
            
            # Suggestions pour l'entraînement
            self.stdout.write(self.style.WARNING("\n💡 Prochaines étapes:"))
            self.stdout.write("1. python manage.py train_ml_model --dataset-path " + dataset_info['dataset_path'])
            self.stdout.write("2. python manage.py train_ml_model --epochs 100 --use-augmentation")
            self.stdout.write("3. python manage.py test_ml_model")
            
        else:
            self.stdout.write(self.style.ERROR('❌ Erreur lors de la préparation du dataset'))

    def list_datasets(self):
        """Affiche la liste des datasets disponibles"""
        self.stdout.write(self.style.SUCCESS('📋 Datasets disponibles:'))
        
        datasets = dataset_downloader.get_available_datasets()
        
        for name, info in datasets.items():
            self.stdout.write(f"\n🔸 {name}")
            self.stdout.write(f"   📝 {info['name']}")
            self.stdout.write(f"   📄 {info['description']}")
            self.stdout.write(f"   💾 Taille: {info['size']}")
            self.stdout.write(f"   🏷️  Classes: {', '.join(info['classes'])}")
        
        # Dataset synthétique spécial
        self.stdout.write(f"\n🔸 synthetic_demo")
        self.stdout.write(f"   📝 Dataset Synthétique de Démonstration")
        self.stdout.write(f"   📄 300 images générées algorithmiquement")
        self.stdout.write(f"   💾 Taille: ~50MB")
        self.stdout.write(f"   🏷️  Classes: normal, oily, dry, combination, sensitive, acne_prone")
        self.stdout.write(f"   ⚡ Recommandé pour débuter")
        
        self.stdout.write(self.style.WARNING("\n⚠️  Note importante:"))
        self.stdout.write("Les vrais datasets médicaux nécessitent des accords de licence.")
        self.stdout.write("Commencez avec 'synthetic_demo' pour tester le système.")
        
        self.stdout.write(self.style.SUCCESS("\n🚀 Usage:"))
        self.stdout.write("python manage.py download_datasets --dataset synthetic_demo")

{% extends 'skin_analysis/base.html' %}
{% block title %}Statut ML - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="text-center mb-5">
        <h2 class="text-white">
          <i class="fas fa-brain"></i> Statut du Modèle ML
        </h2>
        <p class="text-white-50">État actuel de l'intelligence artificielle</p>
      </div>

      <!-- Statut du Modèle -->
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h4><i class="fas fa-check-circle"></i> Modèle ML Actif</h4>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>🧠 Informations du Modèle</h6>
              <ul class="list-unstyled">
                <li><strong>Architecture:</strong> EfficientNetB3</li>
                <li><strong>Dataset:</strong> Synthétique (300 images)</li>
                <li><strong>Classes:</strong> 6 types de peau</li>
                <li><strong>Précision:</strong> ~30% (en amélioration)</li>
                <li><strong>Statut:</strong> <span class="badge bg-success">Opérationnel</span></li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6>📊 Métriques d'Entraînement</h6>
              <ul class="list-unstyled">
                <li><strong>Époques:</strong> 3/50</li>
                <li><strong>Échantillons d'entraînement:</strong> 240</li>
                <li><strong>Échantillons de validation:</strong> 60</li>
                <li><strong>Perte finale:</strong> 1.79</li>
                <li><strong>Dernière mise à jour:</strong> <span id="lastUpdate">Aujourd'hui</span></li>
              </ul>
            </div>
          </div>
          
          <div class="alert alert-info mt-3">
            <h6><i class="fas fa-info-circle"></i> Évolution vs Algorithme CV</h6>
            <div class="row">
              <div class="col-md-6">
                <strong>🔬 Algorithme CV (Ancien):</strong>
                <ul>
                  <li>Précision: ~70-75%</li>
                  <li>Basé sur règles fixes</li>
                  <li>Pas d'apprentissage</li>
                </ul>
              </div>
              <div class="col-md-6">
                <strong>🧠 ML EfficientNet (Nouveau):</strong>
                <ul>
                  <li>Précision: 30% → 95%+ (cible)</li>
                  <li>Apprentissage continu</li>
                  <li>Amélioration avec données</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance par Type -->
      <div class="card mb-4">
        <div class="card-header bg-info text-white">
          <h4><i class="fas fa-chart-bar"></i> Performance par Type de Peau</h4>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>✅ Types Bien Détectés</h6>
              <div class="progress mb-2">
                <div class="progress-bar bg-success" style="width: 100%">
                  Peau Mixte - 100%
                </div>
              </div>
              <small class="text-muted">Le modèle excelle sur ce type</small>
            </div>
            <div class="col-md-6">
              <h6>⚠️ Types à Améliorer</h6>
              <div class="progress mb-1">
                <div class="progress-bar bg-warning" style="width: 0%">
                  Peau Normale - 0%
                </div>
              </div>
              <div class="progress mb-1">
                <div class="progress-bar bg-warning" style="width: 0%">
                  Peau Acnéique - 0%
                </div>
              </div>
              <div class="progress mb-2">
                <div class="progress-bar bg-warning" style="width: 0%">
                  Peau Sensible - 0%
                </div>
              </div>
              <small class="text-muted">Nécessitent plus de données d'entraînement</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Dataset Information -->
      <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
          <h4><i class="fas fa-database"></i> Dataset Actuel</h4>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <h6>🚧 Dataset Synthétique en Cours</h6>
            <p>Actuellement, le modèle utilise un <strong>dataset synthétique</strong> de 300 images générées algorithmiquement.</p>
          </div>
          
          <div class="row">
            <div class="col-md-4">
              <h6>📊 Répartition Actuelle</h6>
              <ul>
                <li>Peau Normale: 50 images</li>
                <li>Peau Grasse: 50 images</li>
                <li>Peau Sèche: 50 images</li>
                <li>Peau Mixte: 50 images</li>
                <li>Peau Sensible: 50 images</li>
                <li>Peau Acnéique: 50 images</li>
              </ul>
            </div>
            <div class="col-md-4">
              <h6>🎯 Objectif Dataset Réel</h6>
              <ul>
                <li>10,000+ images réelles</li>
                <li>Annotations dermatologues</li>
                <li>Diversité ethnique</li>
                <li>Conditions d'éclairage variées</li>
                <li>Validation clinique</li>
              </ul>
            </div>
            <div class="col-md-4">
              <h6>📈 Progression</h6>
              <div class="progress mb-2">
                <div class="progress-bar" style="width: 3%">
                  Dataset: 3%
                </div>
              </div>
              <div class="progress mb-2">
                <div class="progress-bar bg-info" style="width: 15%">
                  Modèle: 15%
                </div>
              </div>
              <div class="progress">
                <div class="progress-bar bg-success" style="width: 85%">
                  Infrastructure: 85%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Commandes Utiles -->
      <div class="card mb-4">
        <div class="card-header bg-dark text-white">
          <h4><i class="fas fa-terminal"></i> Commandes de Gestion</h4>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>🔄 Entraînement</h6>
              <div class="bg-dark text-light p-2 rounded mb-2">
                <code>python manage.py train_ml_model --use-real-dataset --epochs 50</code>
              </div>
              <small>Entraîner avec plus d'époques</small>
              
              <div class="bg-dark text-light p-2 rounded mb-2 mt-3">
                <code>python manage.py train_ml_model --use-augmentation</code>
              </div>
              <small>Utiliser l'augmentation de données</small>
            </div>
            <div class="col-md-6">
              <h6>🧪 Test et Évaluation</h6>
              <div class="bg-dark text-light p-2 rounded mb-2">
                <code>python manage.py test_ml_model --num-samples 50</code>
              </div>
              <small>Tester sur plus d'échantillons</small>
              
              <div class="bg-dark text-light p-2 rounded mb-2 mt-3">
                <code>python manage.py download_datasets --list</code>
              </div>
              <small>Voir les datasets disponibles</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Roadmap -->
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h4><i class="fas fa-road"></i> Roadmap d'Amélioration</h4>
        </div>
        <div class="card-body">
          <div class="timeline">
            <div class="row">
              <div class="col-md-3">
                <div class="card border-success">
                  <div class="card-header bg-success text-white">
                    <h6>✅ Phase 1 - Terminée</h6>
                  </div>
                  <div class="card-body">
                    <ul class="small">
                      <li>Infrastructure ML</li>
                      <li>Dataset synthétique</li>
                      <li>Modèle EfficientNet</li>
                      <li>Pipeline d'entraînement</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card border-warning">
                  <div class="card-header bg-warning text-dark">
                    <h6>🔄 Phase 2 - En Cours</h6>
                  </div>
                  <div class="card-body">
                    <ul class="small">
                      <li>Collecte données réelles</li>
                      <li>Amélioration précision</li>
                      <li>Feedback utilisateurs</li>
                      <li>Optimisation modèle</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card border-info">
                  <div class="card-header bg-info text-white">
                    <h6>⏳ Phase 3 - Prochaine</h6>
                  </div>
                  <div class="card-body">
                    <ul class="small">
                      <li>Dataset 10,000+ images</li>
                      <li>Validation dermatologues</li>
                      <li>Précision >90%</li>
                      <li>Temps réel <1s</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card border-secondary">
                  <div class="card-header bg-secondary text-white">
                    <h6>🚀 Phase 4 - Future</h6>
                  </div>
                  <div class="card-body">
                    <ul class="small">
                      <li>Certification médicale</li>
                      <li>API professionnelle</li>
                      <li>Intégration cliniques</li>
                      <li>IA conversationnelle</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="text-center">
        <a href="{% url 'skin_analysis:camera_test' %}" class="btn btn-success btn-lg">
          <i class="fas fa-camera"></i> Tester le Modèle ML
        </a>
        <a href="{% url 'skin_analysis:dataset_info' %}" class="btn btn-info btn-lg ms-2">
          <i class="fas fa-info-circle"></i> Méthodologie Détaillée
        </a>
        <a href="{% url 'skin_analysis:home' %}" class="btn btn-outline-light btn-lg ms-2">
          <i class="fas fa-home"></i> Accueil
        </a>
      </div>
    </div>
  </div>
</div>

<script>
// Mettre à jour la date de dernière mise à jour
document.getElementById('lastUpdate').textContent = new Date().toLocaleDateString('fr-FR');
</script>
{% endblock %}

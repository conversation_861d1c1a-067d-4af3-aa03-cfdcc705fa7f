from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Avg, Min, Max
from datetime import datetime, timedelta
from .models import ProductPrice, PriceAlert, Store, PriceHistory
from products.models import Product

def price_dashboard(request):
    """Tableau de bord des prix"""
    # Dernières mises à jour de prix
    recent_prices = ProductPrice.objects.filter(
        is_active=True
    ).select_related('product', 'store').order_by('-date_recorded')[:20]

    # Meilleures offres
    best_deals = ProductPrice.objects.filter(
        is_active=True,
        is_on_sale=True
    ).select_related('product', 'store').order_by('-discount_percentage')[:10]

    context = {
        'recent_prices': recent_prices,
        'best_deals': best_deals,
    }
    return render(request, 'price_tracking/dashboard.html', context)

def product_price_history(request, product_id):
    """Historique des prix d'un produit"""
    product = get_object_or_404(Product, id=product_id)

    # Historique des prix des 30 derniers jours
    thirty_days_ago = datetime.now() - timedelta(days=30)
    price_history = ProductPrice.objects.filter(
        product=product,
        date_recorded__gte=thirty_days_ago
    ).select_related('store').order_by('-date_recorded')

    # Statistiques
    stats = ProductPrice.objects.filter(
        product=product,
        is_active=True
    ).aggregate(
        avg_price=Avg('price'),
        min_price=Min('price'),
        max_price=Max('price')
    )

    # Prix par magasin
    stores_prices = {}
    for price in price_history:
        if price.store.name not in stores_prices:
            stores_prices[price.store.name] = []
        stores_prices[price.store.name].append({
            'price': float(price.price),
            'date': price.date_recorded.strftime('%Y-%m-%d'),
            'is_on_sale': price.is_on_sale
        })

    context = {
        'product': product,
        'price_history': price_history,
        'stats': stats,
        'stores_prices': stores_prices,
    }
    return render(request, 'price_tracking/product_history.html', context)

@login_required
def price_alerts(request):
    """Gestion des alertes de prix"""
    alerts = PriceAlert.objects.filter(
        user=request.user,
        is_active=True
    ).select_related('product')

    context = {
        'alerts': alerts,
    }
    return render(request, 'price_tracking/alerts.html', context)

@login_required
def create_price_alert(request, product_id):
    """Créer une alerte de prix"""
    product = get_object_or_404(Product, id=product_id)

    if request.method == 'POST':
        target_price = request.POST.get('target_price')

        try:
            target_price = float(target_price)

            # Vérifier si une alerte existe déjà
            existing_alert = PriceAlert.objects.filter(
                user=request.user,
                product=product,
                is_active=True
            ).first()

            if existing_alert:
                existing_alert.target_price = target_price
                existing_alert.save()
                messages.success(request, 'Alerte de prix mise à jour!')
            else:
                PriceAlert.objects.create(
                    user=request.user,
                    product=product,
                    target_price=target_price
                )
                messages.success(request, 'Alerte de prix créée!')

            return redirect('price_tracking:product_history', product_id=product.id)

        except ValueError:
            messages.error(request, 'Prix invalide!')

    return redirect('price_tracking:product_history', product_id=product.id)

@login_required
def delete_price_alert(request, alert_id):
    """Supprimer une alerte de prix"""
    alert = get_object_or_404(PriceAlert, id=alert_id, user=request.user)
    alert.is_active = False
    alert.save()

    messages.success(request, 'Alerte supprimée!')
    return redirect('price_tracking:alerts')

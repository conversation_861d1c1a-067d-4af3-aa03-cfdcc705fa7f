{% extends 'skin_analysis/base.html' %}

{% block title %}Mes Alertes de Prix - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-white">
                    <i class="fas fa-bell"></i> Mes Alertes de Prix
                </h2>
                <a href="{% url 'products:list' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Ajouter une Alerte
                </a>
            </div>
        </div>
    </div>
    
    {% if alerts %}
    <div class="row">
        {% for alert in alerts %}
        <div class="col-lg-6 mb-4">
            <div class="card h-100" style="border-radius: 15px; border: none; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h5 class="card-title">{{ alert.product.name }}</h5>
                            <p class="text-muted mb-2">
                                <i class="fas fa-building"></i> {{ alert.product.brand.name }}
                            </p>
                        </div>
                        <div class="text-end">
                            {% if alert.is_active %}
                            <span class="badge bg-success">
                                <i class="fas fa-bell"></i> Active
                            </span>
                            {% else %}
                            <span class="badge bg-secondary">
                                <i class="fas fa-bell-slash"></i> Inactive
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">Prix cible</small>
                            <div class="h5 text-warning">
                                <i class="fas fa-target"></i> {{ alert.target_price|floatformat:3 }} TND
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Prix actuel</small>
                            {% with current_price=alert.product.get_current_price %}
                            {% if current_price %}
                            <div class="h5 {% if current_price <= alert.target_price %}text-success{% else %}text-danger{% endif %}">
                                <i class="fas fa-tag"></i> {{ current_price|floatformat:3 }} TND
                            </div>
                            {% else %}
                            <div class="h5 text-muted">
                                <i class="fas fa-question"></i> Non disponible
                            </div>
                            {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                    
                    {% with current_price=alert.product.get_current_price %}
                    {% if current_price and current_price <= alert.target_price %}
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle"></i> 
                        <strong>Objectif atteint!</strong> Le prix est maintenant à {{ current_price|floatformat:3 }} TND
                    </div>
                    {% endif %}
                    {% endwith %}
                    
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> Créée le {{ alert.created_at|date:"d/m/Y à H:i" }}
                        </small>
                        {% if alert.triggered_at %}
                        <br>
                        <small class="text-success">
                            <i class="fas fa-bell"></i> Déclenchée le {{ alert.triggered_at|date:"d/m/Y à H:i" }}
                        </small>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex gap-2">
                        <a href="{% url 'price_tracking:product_history' alert.product.id %}" 
                           class="btn btn-primary btn-sm flex-grow-1">
                            <i class="fas fa-chart-line"></i> Voir Prix
                        </a>
                        <a href="{% url 'products:detail' alert.product.id %}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-info"></i> Détails
                        </a>
                        <a href="{% url 'price_tracking:delete_alert' alert.id %}" 
                           class="btn btn-outline-danger btn-sm"
                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette alerte ?')">
                            <i class="fas fa-trash"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Statistics -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-body">
                    <i class="fas fa-bell fa-2x text-primary mb-2"></i>
                    <h4>{{ alerts.count }}</h4>
                    <p class="text-muted mb-0">Alertes Actives</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4>
                        {% with triggered_count=alerts|length %}
                        {{ triggered_count }}
                        {% endwith %}
                    </h4>
                    <p class="text-muted mb-0">Objectifs Atteints</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-body">
                    <i class="fas fa-percentage fa-2x text-warning mb-2"></i>
                    <h4>
                        {% if alerts %}
                        {% with total=alerts.count triggered=0 %}
                        {{ triggered|div:total|mul:100|floatformat:0 }}%
                        {% endwith %}
                        {% else %}
                        0%
                        {% endif %}
                    </h4>
                    <p class="text-muted mb-0">Taux de Réussite</p>
                </div>
            </div>
        </div>
    </div>
    
    {% else %}
    <!-- No Alerts -->
    <div class="row">
        <div class="col-12">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95); padding: 60px 20px;">
                <i class="fas fa-bell-slash fa-5x text-muted mb-4"></i>
                <h3>Aucune Alerte de Prix</h3>
                <p class="text-muted mb-4">
                    Créez des alertes pour être notifié quand le prix de vos produits préférés baisse.
                </p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="{% url 'products:list' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-search"></i> Parcourir les Produits
                    </a>
                    {% if user.userprofile.skin_type %}
                    <a href="{% url 'products:recommendations' user.userprofile.skin_type.name %}" 
                       class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-star"></i> Mes Recommandations
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Tips -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card" style="border-radius: 15px; background: rgba(255,255,255,0.95);">
                <div class="card-body">
                    <h5><i class="fas fa-lightbulb text-warning"></i> Conseils pour les Alertes de Prix</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Définissez un prix réaliste basé sur l'historique</li>
                                <li><i class="fas fa-check text-success"></i> Surveillez les périodes de promotion</li>
                                <li><i class="fas fa-check text-success"></i> Créez plusieurs alertes pour comparer</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-info text-info"></i> Les prix sont mis à jour quotidiennement</li>
                                <li><i class="fas fa-info text-info"></i> Vous recevrez un email quand l'objectif est atteint</li>
                                <li><i class="fas fa-info text-info"></i> Les alertes restent actives jusqu'à suppression</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

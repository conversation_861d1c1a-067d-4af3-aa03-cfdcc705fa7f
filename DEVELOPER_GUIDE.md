# Guide Développeur - SkinCare Tunisia

## 🏗️ Architecture du Projet

### Structure des Applications

```
skincare_analyzer/
├── skin_analysis/          # Analyse de peau et utilisateurs
│   ├── models.py          # SkinType, SkinAnalysis, UserProfile
│   ├── views.py           # Vues d'analyse et API
│   ├── utils.py           # Fonctions d'analyse IA
│   └── forms.py           # Formulaires d'analyse
├── products/              # Gestion des produits
│   ├── models.py          # Product, Brand, ProductRecommendation
│   └── views.py           # Vues produits et recommandations
├── price_tracking/        # Suivi des prix
│   ├── models.py          # ProductPrice, PriceAlert, Store
│   └── views.py           # Historique prix et alertes
└── static/                # Fichiers statiques
```

## 🧠 Algorithme d'Analyse de Peau

### Analyse par Formulaire
L'algorithme utilise un système de scoring pondéré :

```python
def analyze_form_responses(responses, questions):
    skin_type_scores = {
        'oily': 0, 'dry': 0, 'combination': 0,
        'sensitive': 0, 'normal': 0, 'acne_prone': 0
    }
    
    for question in questions:
        weight = question.weight
        mapping = question.skin_type_mapping
        # Calcul des scores basé sur les réponses
```

### Analyse par Image
L'analyse d'image utilise OpenCV et des techniques de vision par ordinateur :

1. **Préprocessing** : Redimensionnement, validation
2. **Détection de région** : Focus sur la zone centrale du visage
3. **Analyse couleur** : Moyennes RGB, dominance rouge
4. **Analyse texture** : Variance Laplacienne pour détecter imperfections
5. **Analyse HSV** : Brillance et saturation
6. **Classification** : Système de scoring multi-critères

```python
# Critères principaux
oily_score += 2 if bright_ratio > 0.3 else 0
dry_score += 2 if bright_ratio < 0.1 else 0
sensitive_score += 2 if red_dominance > 1.2 else 0
acne_score += 2 if laplacian_var > 1200 else 0
```

## 📱 Gestion des Caméras

### Détection Multi-Caméras
Le système détecte automatiquement :
- Caméras intégrées (webcam laptop)
- Caméras USB externes
- Téléphones connectés via USB
- Caméras virtuelles (DroidCam, etc.)

### Contraintes Adaptatives
```javascript
// Contraintes haute qualité
const constraints = {
    video: {
        width: { ideal: 1280, min: 640, max: 1920 },
        height: { ideal: 720, min: 480, max: 1080 },
        frameRate: { ideal: 30, min: 15, max: 60 }
    }
};

// Fallback contraintes basiques
const basicConstraints = { video: true };
```

## 🔧 API Endpoints

### Analyse d'Image
```
POST /api/analyze-image/
Content-Type: multipart/form-data

Paramètres:
- image: fichier image (JPG, PNG, WebP)

Réponse:
{
    "skin_type": "oily",
    "confidence": 0.75,
    "analysis_data": {
        "brightness": 145.2,
        "texture_variance": 856.3,
        "scores": {...}
    }
}
```

### Recommandations
```
GET /api/get-recommendations/?skin_type=oily

Réponse:
{
    "recommendations": [
        {
            "product_name": "Gel Nettoyant",
            "brand": "La Roche-Posay",
            "reason": "Contrôle l'excès de sébum",
            "priority": 1
        }
    ]
}
```

## 🎨 Frontend Architecture

### Technologies
- **Bootstrap 5** : Framework CSS responsive
- **JavaScript ES6** : Logique interactive
- **WebRTC API** : Accès caméra
- **Canvas API** : Capture d'images
- **Fetch API** : Requêtes AJAX

### Composants Principaux
1. **CameraManager** : Gestion des caméras
2. **ImageAnalyzer** : Capture et envoi pour analyse
3. **ResultsDisplay** : Affichage des résultats
4. **ErrorHandler** : Gestion des erreurs

## 🗄️ Modèles de Données

### SkinAnalysis
```python
class SkinAnalysis(models.Model):
    user = models.ForeignKey(User)
    method = models.CharField(choices=['form', 'camera', 'upload'])
    detected_skin_type = models.ForeignKey(SkinType)
    confidence_score = models.FloatField()
    analysis_data = models.JSONField()  # Données détaillées
    form_responses = models.JSONField()  # Réponses formulaire
```

### ProductRecommendation
```python
class ProductRecommendation(models.Model):
    skin_type = models.ForeignKey(SkinType)
    product = models.ForeignKey(Product)
    priority = models.PositiveIntegerField()  # 1 = haute priorité
    reason = models.TextField()  # Justification
```

## 🧪 Tests

### Tests d'Analyse
```python
def test_form_response_analysis(self):
    responses = {self.question.id: 'Grasse'}
    skin_type, confidence = analyze_form_responses(responses, questions)
    self.assertEqual(skin_type.name, 'oily')
    self.assertGreater(confidence, 0)
```

### Tests d'API
```python
def test_analyze_image_api(self):
    with open('test_image.jpg', 'rb') as img:
        response = self.client.post('/api/analyze-image/', {'image': img})
    self.assertEqual(response.status_code, 200)
```

## 🚀 Déploiement

### Variables d'Environnement
```bash
# Production
DEBUG=False
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://...
ALLOWED_HOSTS=yourdomain.com

# Optionnel
MEDIA_ROOT=/path/to/media
STATIC_ROOT=/path/to/static
```

### Configuration HTTPS
Pour la caméra en production, HTTPS est requis :
```python
# settings.py
SECURE_SSL_REDIRECT = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
```

## 🔍 Debugging

### Logs d'Analyse
```python
# Dans utils.py
import logging
logger = logging.getLogger(__name__)

def analyze_image_basic(image_path):
    logger.info(f"Analyzing image: {image_path}")
    # ... analyse
    logger.info(f"Result: {skin_type}, confidence: {confidence}")
```

### Debug Caméra
```javascript
// Dans camera_analysis.html
console.log('Caméras détectées:', availableCameras);
console.log('Contraintes utilisées:', constraints);
console.log('Settings caméra:', track.getSettings());
```

## 📈 Optimisations

### Performance Image
1. **Redimensionnement** : Limiter à 1024px max
2. **Compression** : JPEG qualité 90%
3. **Cache** : Stocker les résultats d'analyse
4. **Async** : Traitement en arrière-plan

### Base de Données
```python
# Indexes pour performance
class Meta:
    indexes = [
        models.Index(fields=['product', '-date_recorded']),
        models.Index(fields=['user', '-created_at']),
    ]
```

## 🛠️ Extensions Possibles

### IA Avancée
- Intégration TensorFlow.js pour analyse côté client
- Modèles pré-entraînés pour détection faciale
- Analyse de texture plus sophistiquée

### Fonctionnalités
- Historique d'évolution de la peau
- Recommandations saisonnières
- Intégration réseaux sociaux
- Mode sombre/clair

### Intégrations
- API e-commerce tunisiens
- Système de notifications push
- Géolocalisation pour magasins proches
- Chatbot IA pour conseils

## 🤝 Contribution

### Standards de Code
- PEP 8 pour Python
- ESLint pour JavaScript
- Black pour formatage automatique
- Type hints recommandés

### Workflow Git
1. Fork du repository
2. Branche feature/fix
3. Tests passants
4. Pull request avec description détaillée

### Tests Requis
- Couverture > 80%
- Tests unitaires pour nouvelles fonctions
- Tests d'intégration pour API
- Tests frontend pour interactions caméra

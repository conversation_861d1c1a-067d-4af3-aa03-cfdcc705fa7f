"""
Détecteur de visage et de peau pour validation avant analyse
Évite l'analyse d'objets non pertinents
"""

import cv2
import numpy as np
import os
from django.conf import settings

class FaceSkinDetector:
    """Détecteur de visage et de peau pour validation"""
    
    def __init__(self):
        # Charger les classificateurs Haar Cascade
        self.face_cascade = None
        self.load_face_detector()
        
        # Seuils de validation
        self.validation_thresholds = {
            'min_face_area': 0.02,      # 2% de l'image minimum
            'max_face_area': 0.8,       # 80% de l'image maximum
            'min_skin_ratio': 0.15,     # 15% de pixels peau minimum
            'max_skin_ratio': 0.85,     # 85% de pixels peau maximum
            'min_face_confidence': 0.3,  # Confiance minimum détection visage
            'skin_color_variance': 30    # Variance couleur peau acceptable
        }
    
    def load_face_detector(self):
        """Charge le détecteur de visage Haar Cascade"""
        try:
            # Essayer de charger depuis OpenCV
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            if os.path.exists(cascade_path):
                self.face_cascade = cv2.CascadeClassifier(cascade_path)
                print("✅ Détecteur de visage chargé avec succès")
            else:
                print("⚠️ Détecteur de visage non trouvé, utilisation de méthodes alternatives")
                self.face_cascade = None
        except Exception as e:
            print(f"⚠️ Erreur chargement détecteur de visage: {e}")
            self.face_cascade = None
    
    def validate_image_for_skin_analysis(self, image_path):
        """Valide si l'image contient un visage/peau analysable"""
        try:
            # Charger l'image
            image = cv2.imread(image_path)
            if image is None:
                return False, "Image non valide ou corrompue"
            
            # Vérifications multiples
            face_detected, face_message = self.detect_face(image)
            skin_detected, skin_message = self.detect_skin_regions(image)
            content_valid, content_message = self.validate_image_content(image)
            
            # Logique de validation combinée
            if face_detected and skin_detected:
                return True, "✅ Visage et peau détectés - Analyse possible"
            elif face_detected:
                return True, "✅ Visage détecté - Analyse possible"
            elif skin_detected and content_valid:
                return True, "✅ Zones de peau détectées - Analyse possible"
            else:
                # Construire message d'erreur détaillé
                error_msg = "❌ Image non adaptée pour l'analyse de peau:\n"
                if not face_detected:
                    error_msg += f"• {face_message}\n"
                if not skin_detected:
                    error_msg += f"• {skin_message}\n"
                if not content_valid:
                    error_msg += f"• {content_message}\n"
                error_msg += "\n💡 Conseils:\n"
                error_msg += "• Prenez une photo de votre visage bien éclairé\n"
                error_msg += "• Assurez-vous que votre visage occupe une bonne partie de l'image\n"
                error_msg += "• Évitez les objets, paysages ou autres sujets"
                
                return False, error_msg.strip()
                
        except Exception as e:
            return False, f"❌ Erreur lors de la validation: {str(e)}"
    
    def detect_face(self, image):
        """Détecte la présence d'un visage dans l'image"""
        try:
            if self.face_cascade is None:
                # Méthode alternative sans Haar Cascade
                return self.detect_face_alternative(image)
            
            # Conversion en niveaux de gris
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Détection de visages
            faces = self.face_cascade.detectMultiScale(
                gray, 
                scaleFactor=1.1, 
                minNeighbors=5, 
                minSize=(30, 30)
            )
            
            if len(faces) > 0:
                # Calculer la taille du plus grand visage
                largest_face = max(faces, key=lambda f: f[2] * f[3])
                face_area = largest_face[2] * largest_face[3]
                image_area = image.shape[0] * image.shape[1]
                face_ratio = face_area / image_area
                
                if face_ratio >= self.validation_thresholds['min_face_area']:
                    return True, f"Visage détecté ({face_ratio:.1%} de l'image)"
                else:
                    return False, "Visage trop petit dans l'image"
            else:
                return False, "Aucun visage détecté"
                
        except Exception as e:
            return False, f"Erreur détection visage: {str(e)}"
    
    def detect_face_alternative(self, image):
        """Méthode alternative de détection de visage"""
        try:
            # Analyse des proportions et couleurs typiques d'un visage
            height, width = image.shape[:2]
            
            # Vérifier les proportions (visage généralement plus haut que large)
            aspect_ratio = height / width
            if aspect_ratio < 0.5 or aspect_ratio > 3.0:
                return False, "Proportions d'image non compatibles avec un visage"
            
            # Analyser la distribution des couleurs (tons chair)
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Masque pour tons chair (approximatif)
            lower_skin = np.array([0, 20, 70])
            upper_skin = np.array([20, 255, 255])
            skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            skin_ratio = np.sum(skin_mask > 0) / skin_mask.size
            
            if skin_ratio >= 0.2:  # Au moins 20% de tons chair
                return True, f"Zones de peau détectées ({skin_ratio:.1%})"
            else:
                return False, "Peu de tons chair détectés"
                
        except Exception as e:
            return False, f"Erreur détection alternative: {str(e)}"
    
    def detect_skin_regions(self, image):
        """Détecte les régions de peau dans l'image"""
        try:
            # Conversion en espace HSV pour meilleure détection de peau
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Masques pour différents tons de peau
            # Tons clairs
            lower_light = np.array([0, 10, 60])
            upper_light = np.array([20, 150, 255])
            mask_light = cv2.inRange(hsv, lower_light, upper_light)
            
            # Tons moyens
            lower_medium = np.array([0, 20, 80])
            upper_medium = np.array([25, 255, 255])
            mask_medium = cv2.inRange(hsv, lower_medium, upper_medium)
            
            # Tons foncés
            lower_dark = np.array([0, 30, 50])
            upper_dark = np.array([30, 255, 200])
            mask_dark = cv2.inRange(hsv, lower_dark, upper_dark)
            
            # Combiner tous les masques
            skin_mask = cv2.bitwise_or(mask_light, mask_medium)
            skin_mask = cv2.bitwise_or(skin_mask, mask_dark)
            
            # Nettoyer le masque (éliminer le bruit)
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)
            
            # Calculer le ratio de peau
            skin_pixels = np.sum(skin_mask > 0)
            total_pixels = skin_mask.size
            skin_ratio = skin_pixels / total_pixels
            
            # Validation
            min_ratio = self.validation_thresholds['min_skin_ratio']
            max_ratio = self.validation_thresholds['max_skin_ratio']
            
            if skin_ratio >= min_ratio and skin_ratio <= max_ratio:
                return True, f"Zones de peau valides ({skin_ratio:.1%})"
            elif skin_ratio < min_ratio:
                return False, f"Trop peu de peau détectée ({skin_ratio:.1%})"
            else:
                return False, f"Trop de peau détectée ({skin_ratio:.1%}) - Image possiblement surexposée"
                
        except Exception as e:
            return False, f"Erreur détection peau: {str(e)}"
    
    def validate_image_content(self, image):
        """Valide le contenu général de l'image"""
        try:
            height, width = image.shape[:2]
            
            # Vérifier la taille minimale
            if width < 100 or height < 100:
                return False, "Image trop petite (minimum 100x100 pixels)"
            
            # Vérifier que l'image n'est pas complètement noire ou blanche
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            mean_brightness = np.mean(gray)
            
            if mean_brightness < 10:
                return False, "Image trop sombre"
            elif mean_brightness > 245:
                return False, "Image trop claire/surexposée"
            
            # Vérifier la variance (éviter les images uniformes)
            variance = np.var(gray)
            if variance < 100:
                return False, "Image trop uniforme (manque de détails)"
            
            # Vérifier les couleurs (éviter les images monochromes artificielles)
            b, g, r = cv2.split(image)
            color_variance = np.var([np.mean(b), np.mean(g), np.mean(r)])
            
            if color_variance < 10:
                return False, "Image trop monochrome"
            
            return True, "Contenu d'image valide"
            
        except Exception as e:
            return False, f"Erreur validation contenu: {str(e)}"
    
    def get_validation_suggestions(self, image_path):
        """Fournit des suggestions pour améliorer l'image"""
        suggestions = [
            "📸 Prenez une photo de votre visage bien centré",
            "💡 Assurez-vous d'avoir un bon éclairage naturel",
            "🎯 Votre visage doit occuper une bonne partie de l'image",
            "🚫 Évitez les objets, paysages ou autres sujets",
            "📱 Tenez votre appareil stable pour éviter le flou",
            "🌟 Nettoyez l'objectif de votre caméra"
        ]
        return suggestions

# Instance globale
face_skin_detector = FaceSkinDetector()

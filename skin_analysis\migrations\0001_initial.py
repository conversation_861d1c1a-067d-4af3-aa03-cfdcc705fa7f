# Generated by Django 5.2.1 on 2025-06-25 13:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SkinAnalysisQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_text', models.TextField()),
                ('question_type', models.CharField(choices=[('single_choice', 'Choix unique'), ('multiple_choice', 'Choix multiple'), ('scale', 'Échelle'), ('text', 'Texte libre')], max_length=20)),
                ('choices', models.JSONField(default=list)),
                ('weight', models.FloatField(default=1.0)),
                ('skin_type_mapping', models.JSONField(default=dict)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='SkinType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('oily', 'Peau Grasse'), ('dry', 'Peau Sèche'), ('combination', 'Peau Mixte'), ('sensitive', 'Peau Sensible'), ('normal', 'Peau Normale'), ('acne_prone', 'Peau à Tendance Acnéique')], max_length=50, unique=True)),
                ('description', models.TextField()),
                ('characteristics', models.JSONField(default=dict)),
                ('care_tips', models.TextField()),
            ],
        ),
        migrations.CreateModel(
            name='SkinAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('method', models.CharField(choices=[('form', 'Formulaire'), ('camera', 'Caméra'), ('upload', "Upload d'image")], max_length=20)),
                ('image', models.ImageField(blank=True, null=True, upload_to='skin_analysis/')),
                ('confidence_score', models.FloatField(blank=True, null=True)),
                ('analysis_data', models.JSONField(default=dict)),
                ('form_responses', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('detected_skin_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='skin_analysis.skintype')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('age', models.PositiveIntegerField(blank=True, null=True)),
                ('skin_concerns', models.JSONField(default=list)),
                ('allergies', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('skin_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='skin_analysis.skintype')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

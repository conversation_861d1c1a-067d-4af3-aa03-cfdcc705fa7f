from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login
from django.contrib.auth.forms import UserCreationForm
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import json
import cv2
import numpy as np
from PIL import Image
import io
import base64

from .models import SkinType, SkinAnalysis, SkinAnalysisQuestion, UserProfile
from .forms import SkinAnalysisForm, UserProfileForm, QuestionResponseForm
from .utils import analyze_form_responses, analyze_image_basic, get_skin_care_recommendations
from products.models import Product, ProductRecommendation

def home(request):
    """Page d'accueil"""
    return render(request, 'skin_analysis/home.html')

def register(request):
    """Inscription utilisateur"""
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Créer le profil utilisateur
            UserProfile.objects.create(user=user)
            login(request, user)
            messages.success(request, 'Compte créé avec succès!')
            return redirect('skin_analysis:profile')
    else:
        form = UserCreationForm()
    return render(request, 'registration/register.html', {'form': form})

@login_required
def profile(request):
    """Profil utilisateur"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=profile)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profil mis à jour!')
            return redirect('skin_analysis:profile')
    else:
        form = UserProfileForm(instance=profile)

    # Récupérer les analyses récentes
    recent_analyses = SkinAnalysis.objects.filter(user=request.user)[:5]

    context = {
        'form': form,
        'profile': profile,
        'recent_analyses': recent_analyses,
    }
    return render(request, 'skin_analysis/profile.html', context)

def skin_analysis_form(request):
    """Analyse de peau par formulaire"""
    questions = SkinAnalysisQuestion.objects.filter(is_active=True)

    if request.method == 'POST':
        form = SkinAnalysisForm(request.POST, request.FILES)
        if form.is_valid():
            # Traiter les réponses du formulaire
            responses = {}
            for question in questions:
                field_name = f'question_{question.id}'
                if field_name in request.POST:
                    responses[question.id] = request.POST[field_name]

            # Analyser les réponses pour déterminer le type de peau
            detected_skin_type, confidence = analyze_form_responses(responses, questions)

            # Sauvegarder l'analyse
            analysis = SkinAnalysis.objects.create(
                user=request.user if request.user.is_authenticated else None,
                method='form',
                image=form.cleaned_data.get('image'),
                detected_skin_type=detected_skin_type,
                confidence_score=confidence,
                form_responses=responses
            )

            # Mettre à jour le profil utilisateur si connecté
            if request.user.is_authenticated:
                profile, created = UserProfile.objects.get_or_create(user=request.user)
                profile.skin_type = detected_skin_type
                profile.save()

            return redirect('skin_analysis:results', analysis_id=analysis.id)
    else:
        form = SkinAnalysisForm()

    context = {
        'form': form,
        'questions': questions,
    }
    return render(request, 'skin_analysis/form_analysis.html', context)

def camera_analysis(request):
    """Analyse de peau par caméra en temps réel"""
    return render(request, 'skin_analysis/camera_analysis.html')

def analysis_results(request, analysis_id):
    """Affichage des résultats d'analyse"""
    analysis = get_object_or_404(SkinAnalysis, id=analysis_id)

    # Récupérer les recommandations de produits
    recommendations = []
    if analysis.detected_skin_type:
        recommendations = ProductRecommendation.objects.filter(
            skin_type=analysis.detected_skin_type
        ).select_related('product', 'product__brand')[:6]

    # Conseils de soins
    care_recommendations = get_skin_care_recommendations(analysis.detected_skin_type) if analysis.detected_skin_type else {}

    context = {
        'analysis': analysis,
        'recommendations': recommendations,
        'care_recommendations': care_recommendations,
    }
    return render(request, 'skin_analysis/results.html', context)

@csrf_exempt
def analyze_image_api(request):
    """API pour analyser une image uploadée"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        if 'image' not in request.FILES:
            return JsonResponse({'error': 'Aucune image fournie'}, status=400)

        image_file = request.FILES['image']

        # Sauvegarder temporairement l'image
        temp_path = default_storage.save(f'temp/{image_file.name}', image_file)
        full_path = default_storage.path(temp_path)

        # Analyser l'image
        skin_type, confidence, analysis_data = analyze_image_basic(full_path)

        # Nettoyer le fichier temporaire
        default_storage.delete(temp_path)

        return JsonResponse({
            'skin_type': skin_type,
            'confidence': confidence,
            'analysis_data': analysis_data
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

def get_recommendations_api(request):
    """API pour récupérer les recommandations de produits"""
    skin_type_name = request.GET.get('skin_type')

    if not skin_type_name:
        return JsonResponse({'error': 'Type de peau requis'}, status=400)

    try:
        skin_type = SkinType.objects.get(name=skin_type_name)
        recommendations = ProductRecommendation.objects.filter(
            skin_type=skin_type
        ).select_related('product', 'product__brand')[:10]

        data = []
        for rec in recommendations:
            data.append({
                'product_name': rec.product.name,
                'brand': rec.product.brand.name,
                'reason': rec.reason,
                'priority': rec.priority,
                'current_price': str(rec.product.get_current_price()) if rec.product.get_current_price() else None
            })

        return JsonResponse({'recommendations': data})

    except SkinType.DoesNotExist:
        return JsonResponse({'error': 'Type de peau non trouvé'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login
from django.contrib.auth.forms import UserCreationForm
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import json
import cv2
import numpy as np
from PIL import Image
import io
import base64

from .models import SkinType, SkinAnalysis, SkinAnalysisQuestion, UserProfile
from .forms import SkinAnalysisForm, UserProfileForm, QuestionResponseForm
from .utils import analyze_form_responses, analyze_image_basic, get_skin_care_recommendations
from products.models import Product, ProductRecommendation

def home(request):
    """Page d'accueil"""
    return render(request, 'skin_analysis/home.html')

def register(request):
    """Inscription utilisateur"""
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Créer le profil utilisateur
            UserProfile.objects.create(user=user)
            login(request, user)
            messages.success(request, 'Compte créé avec succès!')
            return redirect('skin_analysis:profile')
    else:
        form = UserCreationForm()
    return render(request, 'registration/register.html', {'form': form})

@login_required
def profile(request):
    """Profil utilisateur"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=profile)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profil mis à jour!')
            return redirect('skin_analysis:profile')
    else:
        form = UserProfileForm(instance=profile)

    # Récupérer les analyses récentes
    recent_analyses = SkinAnalysis.objects.filter(user=request.user)[:5]

    context = {
        'form': form,
        'profile': profile,
        'recent_analyses': recent_analyses,
    }
    return render(request, 'skin_analysis/profile.html', context)

def skin_analysis_form(request):
    """Analyse de peau par formulaire"""
    questions = SkinAnalysisQuestion.objects.filter(is_active=True)

    if request.method == 'POST':
        form = SkinAnalysisForm(request.POST, request.FILES)
        if form.is_valid():
            # Traiter les réponses du formulaire
            responses = {}
            for question in questions:
                field_name = f'question_{question.id}'
                if field_name in request.POST:
                    responses[question.id] = request.POST[field_name]

            # Analyser les réponses pour déterminer le type de peau
            detected_skin_type, confidence = analyze_form_responses(responses, questions)

            # Sauvegarder l'analyse
            analysis = SkinAnalysis.objects.create(
                user=request.user if request.user.is_authenticated else None,
                method='form',
                image=form.cleaned_data.get('image'),
                detected_skin_type=detected_skin_type,
                confidence_score=confidence,
                form_responses=responses
            )

            # Mettre à jour le profil utilisateur si connecté
            if request.user.is_authenticated:
                profile, created = UserProfile.objects.get_or_create(user=request.user)
                profile.skin_type = detected_skin_type
                profile.save()

            return redirect('skin_analysis:results', analysis_id=analysis.id)
    else:
        form = SkinAnalysisForm()

    context = {
        'form': form,
        'questions': questions,
    }
    return render(request, 'skin_analysis/form_analysis.html', context)

def camera_analysis(request):
    """Analyse de peau par caméra en temps réel"""
    return render(request, 'skin_analysis/camera_analysis.html')

def camera_test(request):
    """Vue pour tester la caméra (version simplifiée)"""
    return render(request, 'skin_analysis/camera_test.html')

def dataset_info(request):
    """Page d'information sur le dataset et la méthodologie"""
    return render(request, 'skin_analysis/dataset_info.html')

def ml_status(request):
    """Page de statut du modèle ML"""
    return render(request, 'skin_analysis/ml_status.html')

def phone_camera_help(request):
    """Page d'aide pour utiliser le téléphone comme caméra"""
    return render(request, 'skin_analysis/phone_camera_help.html')

@csrf_exempt
def feedback_analysis(request):
    """Collecte le feedback utilisateur pour améliorer le modèle ML"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        import json
        data = json.loads(request.body)

        analysis_id = data.get('analysis_id')
        correct_skin_type = data.get('correct_skin_type')
        user_satisfaction = data.get('satisfaction', 3)  # 1-5
        consent_data_use = data.get('consent', False)

        if not analysis_id or not correct_skin_type:
            return JsonResponse({'error': 'Données manquantes'}, status=400)

        # Récupérer l'analyse
        try:
            analysis = SkinAnalysis.objects.get(id=analysis_id)
        except SkinAnalysis.DoesNotExist:
            return JsonResponse({'error': 'Analyse non trouvée'}, status=404)

        # Collecter les données si consentement donné
        if consent_data_use and hasattr(analysis, 'image_path'):
            from .data_collection import data_collector

            feedback_data = {
                'original_prediction': analysis.detected_skin_type.name if analysis.detected_skin_type else 'unknown',
                'user_correction': correct_skin_type,
                'satisfaction_score': user_satisfaction,
                'confidence_original': analysis.confidence_score
            }

            # Collecter l'échantillon pour améliorer le modèle
            success = data_collector.collect_sample(
                image_path=analysis.image_path,
                skin_type=correct_skin_type,
                user_feedback=feedback_data,
                analysis_data=analysis.analysis_data,
                consent=True
            )

            if success:
                # Mettre à jour l'analyse avec le feedback
                analysis.user_feedback = feedback_data
                analysis.save()

        return JsonResponse({
            'success': True,
            'message': 'Merci pour votre feedback ! Cela nous aide à améliorer notre IA.'
        })

    except Exception as e:
        return JsonResponse({'error': f'Erreur: {str(e)}'}, status=500)

def ml_model_stats(request):
    """Affiche les statistiques du modèle ML et du dataset"""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Accès non autorisé'}, status=403)

    try:
        from .data_collection import data_collector
        from .ml_models import skin_classifier

        # Stats du dataset
        dataset_stats = data_collector.get_dataset_stats()

        # Stats des analyses récentes
        recent_analyses = SkinAnalysis.objects.filter(method='camera').order_by('-created_at')[:100]

        accuracy_by_type = {}
        for analysis in recent_analyses:
            if hasattr(analysis, 'user_feedback') and analysis.user_feedback:
                original = analysis.detected_skin_type.name if analysis.detected_skin_type else 'unknown'
                corrected = analysis.user_feedback.get('user_correction')

                if original not in accuracy_by_type:
                    accuracy_by_type[original] = {'correct': 0, 'total': 0}

                accuracy_by_type[original]['total'] += 1
                if original == corrected:
                    accuracy_by_type[original]['correct'] += 1

        # Calculer les taux de précision
        for skin_type in accuracy_by_type:
            total = accuracy_by_type[skin_type]['total']
            correct = accuracy_by_type[skin_type]['correct']
            accuracy_by_type[skin_type]['accuracy'] = (correct / total * 100) if total > 0 else 0

        stats = {
            'dataset': dataset_stats,
            'model_accuracy': accuracy_by_type,
            'total_analyses': recent_analyses.count(),
            'model_status': 'active' if skin_classifier.model else 'fallback'
        }

        return JsonResponse(stats)

    except Exception as e:
        return JsonResponse({'error': f'Erreur: {str(e)}'}, status=500)

def analysis_results(request, analysis_id):
    """Affichage des résultats d'analyse"""
    analysis = get_object_or_404(SkinAnalysis, id=analysis_id)

    # Récupérer les recommandations de produits
    recommendations = []
    if analysis.detected_skin_type:
        recommendations = ProductRecommendation.objects.filter(
            skin_type=analysis.detected_skin_type
        ).select_related('product', 'product__brand')[:6]

    # Conseils de soins
    care_recommendations = get_skin_care_recommendations(analysis.detected_skin_type) if analysis.detected_skin_type else {}

    context = {
        'analysis': analysis,
        'recommendations': recommendations,
        'care_recommendations': care_recommendations,
    }
    return render(request, 'skin_analysis/results.html', context)

@csrf_exempt
def analyze_image_api(request):
    """API pour analyser une image uploadée"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        if 'image' not in request.FILES:
            return JsonResponse({'error': 'Aucune image fournie'}, status=400)

        image_file = request.FILES['image']

        # Vérifier la taille du fichier (max 10MB)
        if image_file.size > 10 * 1024 * 1024:
            return JsonResponse({'error': 'Image trop volumineuse (max 10MB)'}, status=400)

        # Vérifier le type de fichier
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
        if image_file.content_type not in allowed_types:
            return JsonResponse({'error': 'Format d\'image non supporté. Utilisez JPG, PNG ou WebP.'}, status=400)

        # Sauvegarder temporairement l'image
        import uuid
        unique_filename = f'temp/{uuid.uuid4()}_{image_file.name}'
        temp_path = default_storage.save(unique_filename, image_file)
        full_path = default_storage.path(temp_path)

        try:
            # Utiliser le nouveau modèle ML avancé
            from .ml_models import skin_classifier
            skin_type, confidence, analysis_data = skin_classifier.predict(full_path)

            # Créer une analyse si l'utilisateur est connecté
            if request.user.is_authenticated:
                analysis = SkinAnalysis.objects.create(
                    user=request.user,
                    method='camera',
                    detected_skin_type=SkinType.objects.filter(name=skin_type).first(),
                    confidence_score=confidence,
                    analysis_data=analysis_data
                )
                analysis_data['analysis_id'] = analysis.id

            return JsonResponse({
                'skin_type': skin_type,
                'confidence': confidence,
                'analysis_data': analysis_data,
                'success': True
            })

        finally:
            # Nettoyer le fichier temporaire
            try:
                default_storage.delete(temp_path)
            except:
                pass  # Ignore cleanup errors

    except Exception as e:
        import traceback
        print(f"Error in analyze_image_api: {e}")
        print(traceback.format_exc())
        return JsonResponse({'error': f'Erreur d\'analyse: {str(e)}'}, status=500)

def get_recommendations_api(request):
    """API pour récupérer les recommandations de produits"""
    skin_type_name = request.GET.get('skin_type')

    if not skin_type_name:
        return JsonResponse({'error': 'Type de peau requis'}, status=400)

    try:
        skin_type = SkinType.objects.get(name=skin_type_name)
        recommendations = ProductRecommendation.objects.filter(
            skin_type=skin_type
        ).select_related('product', 'product__brand')[:10]

        data = []
        for rec in recommendations:
            data.append({
                'product_name': rec.product.name,
                'brand': rec.product.brand.name,
                'reason': rec.reason,
                'priority': rec.priority,
                'current_price': str(rec.product.get_current_price()) if rec.product.get_current_price() else None
            })

        return JsonResponse({'recommendations': data})

    except SkinType.DoesNotExist:
        return JsonResponse({'error': 'Type de peau non trouvé'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

{% extends 'skin_analysis/base.html' %}
{% load static %}

{% block title %}Interface Médicale - Analyse Dermatologique{% endblock %}

{% block extra_css %}
<style>
  .medical-interface {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
  }
  
  .medical-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 20px;
  }
  
  .medical-header {
    text-align: center;
    margin-bottom: 30px;
  }
  
  .medical-header h1 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 10px;
  }
  
  .medical-badge {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 20px;
  }
  
  .analysis-section {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .pathology-alert {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
  }
  
  .confidence-meter {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
  }
  
  .confidence-bar {
    height: 25px;
    border-radius: 12px;
    background: linear-gradient(90deg, #ff7675 0%, #fdcb6e 50%, #00b894 100%);
    position: relative;
    overflow: hidden;
  }
  
  .confidence-indicator {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: rgba(255,255,255,0.3);
    border-radius: 12px;
    transition: width 0.8s ease;
  }
  
  .medical-results {
    display: none;
  }
  
  .pathology-finding {
    background: #ffe8e8;
    border-left: 4px solid #ff4757;
    padding: 10px 15px;
    margin: 8px 0;
    border-radius: 0 8px 8px 0;
  }
  
  .clinical-note {
    background: #e8f4fd;
    border-left: 4px solid #3742fa;
    padding: 10px 15px;
    margin: 8px 0;
    border-radius: 0 8px 8px 0;
  }
  
  .diagnostic-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
  }
  
  .diagnostic-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #dee2e6;
  }
  
  .medical-upload {
    border: 3px dashed #74b9ff;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    background: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
  }
  
  .medical-upload:hover {
    border-color: #0984e3;
    background: #e8f4fd;
  }
  
  .medical-upload.dragover {
    border-color: #00b894;
    background: #e8f8f5;
  }
</style>
{% endblock %}

{% block content %}
<div class="medical-interface">
  <div class="container">
    <div class="medical-card">
      <div class="medical-header">
        <h1><i class="fas fa-user-md"></i> Interface Médicale Dermatologique</h1>
        <div class="medical-badge">
          <i class="fas fa-hospital"></i> Grade Hospitalier - Confiance 95-99%
        </div>
        <p class="text-muted">Analyse dermatologique de niveau médical professionnel</p>
      </div>

      <!-- Section Upload -->
      <div class="analysis-section">
        <h3><i class="fas fa-upload"></i> Acquisition d'Image Médicale</h3>
        
        <div class="medical-upload" id="medicalUpload">
          <i class="fas fa-camera-retro fa-3x text-primary mb-3"></i>
          <h4>Télécharger Image Patient</h4>
          <p class="text-muted">Formats acceptés: JPG, PNG, WebP | Taille max: 10MB</p>
          <p class="small"><strong>Recommandations médicales:</strong></p>
          <ul class="text-left small text-muted">
            <li>Éclairage uniforme et naturel</li>
            <li>Distance: 20-30cm du visage</li>
            <li>Résolution minimale: 800x600px</li>
            <li>Éviter les reflets et ombres</li>
          </ul>
          <input type="file" id="medicalImageInput" accept="image/*" style="display: none;">
          <button class="btn btn-primary btn-lg mt-3" onclick="document.getElementById('medicalImageInput').click()">
            <i class="fas fa-folder-open"></i> Sélectionner Image
          </button>
        </div>
      </div>

      <!-- Section Résultats -->
      <div class="medical-results" id="medicalResults">
        <!-- Diagnostic Principal -->
        <div class="analysis-section">
          <h3><i class="fas fa-stethoscope"></i> Diagnostic Principal</h3>
          <div class="confidence-meter">
            <h5>Confiance Diagnostique</h5>
            <div class="confidence-bar">
              <div class="confidence-indicator" id="confidenceIndicator"></div>
            </div>
            <div class="d-flex justify-content-between mt-2">
              <span class="small">Faible (80%)</span>
              <span class="small font-weight-bold" id="confidenceText">---%</span>
              <span class="small">Élevée (99%)</span>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h5><i class="fas fa-diagnosis"></i> Diagnostic: <span id="primaryDiagnosis">---</span></h5>
            <p id="diagnosticDescription">En attente d'analyse...</p>
          </div>
        </div>

        <!-- Analyses Pathologiques -->
        <div class="diagnostic-grid">
          <!-- Analyse Sébacée -->
          <div class="diagnostic-card">
            <h5><i class="fas fa-tint"></i> Analyse Sébacée</h5>
            <div id="sebumAnalysis">
              <p><strong>Grade:</strong> <span id="sebumGrade">---</span></p>
              <p><strong>Ratio sébacé:</strong> <span id="sebumRatio">---</span></p>
              <div id="sebumPathology"></div>
            </div>
          </div>

          <!-- Morphologie Poreuse -->
          <div class="diagnostic-card">
            <h5><i class="fas fa-circle-notch"></i> Morphologie Poreuse</h5>
            <div id="poreAnalysis">
              <p><strong>Grade:</strong> <span id="poreGrade">---</span></p>
              <p><strong>Densité:</strong> <span id="poreDensity">---</span></p>
              <div id="porePathology"></div>
            </div>
          </div>

          <!-- Texture Pathologique -->
          <div class="diagnostic-card">
            <h5><i class="fas fa-texture"></i> Analyse Texturale</h5>
            <div id="textureAnalysis">
              <p><strong>Grade:</strong> <span id="textureGrade">---</span></p>
              <p><strong>Score rugosité:</strong> <span id="roughnessScore">---</span></p>
              <div id="texturePathology"></div>
            </div>
          </div>

          <!-- Analyse Vasculaire -->
          <div class="diagnostic-card">
            <h5><i class="fas fa-heartbeat"></i> Analyse Vasculaire</h5>
            <div id="vascularAnalysis">
              <p><strong>Grade:</strong> <span id="vascularGrade">---</span></p>
              <p><strong>Érythème:</strong> <span id="erythemaLevel">---</span></p>
            </div>
          </div>
        </div>

        <!-- Notes Cliniques -->
        <div class="analysis-section">
          <h3><i class="fas fa-notes-medical"></i> Notes Cliniques</h3>
          <div id="clinicalNotes">
            <p class="text-muted">Aucune note clinique générée</p>
          </div>
        </div>

        <!-- Signes Pathologiques -->
        <div class="analysis-section">
          <h3><i class="fas fa-exclamation-triangle"></i> Signes Pathologiques</h3>
          <div id="pathologicalFindings">
            <p class="text-muted">Aucun signe pathologique détecté</p>
          </div>
        </div>

        <!-- Recommandations Médicales -->
        <div class="analysis-section">
          <h3><i class="fas fa-prescription"></i> Recommandations Médicales</h3>
          <div id="medicalRecommendations">
            <p class="text-muted">Recommandations en cours de génération...</p>
          </div>
        </div>
      </div>

      <!-- Status -->
      <div class="mt-3">
        <div class="alert alert-info" id="medicalStatus">
          <i class="fas fa-info-circle"></i> Prêt pour analyse médicale
        </div>
      </div>
    </div>
  </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const medicalUpload = document.getElementById('medicalUpload');
  const medicalImageInput = document.getElementById('medicalImageInput');
  const medicalResults = document.getElementById('medicalResults');
  const medicalStatus = document.getElementById('medicalStatus');

  // Drag & Drop
  medicalUpload.addEventListener('dragover', function(e) {
    e.preventDefault();
    medicalUpload.classList.add('dragover');
  });

  medicalUpload.addEventListener('dragleave', function(e) {
    e.preventDefault();
    medicalUpload.classList.remove('dragover');
  });

  medicalUpload.addEventListener('drop', function(e) {
    e.preventDefault();
    medicalUpload.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      analyzeMedicalImage(files[0]);
    }
  });

  // Upload via input
  medicalImageInput.addEventListener('change', function(e) {
    if (e.target.files.length > 0) {
      analyzeMedicalImage(e.target.files[0]);
    }
  });

  async function analyzeMedicalImage(file) {
    try {
      updateMedicalStatus('🔬 Analyse médicale en cours...', 'warning');
      
      const formData = new FormData();
      formData.append('image', file);
      formData.append('medical_grade', 'hospital');

      const response = await fetch('{% url "skin_analysis:medical_analysis_api" %}', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
      });

      const data = await response.json();

      if (data.error) {
        updateMedicalStatus('❌ ' + data.error, 'danger');
        return;
      }

      displayMedicalResults(data);
      updateMedicalStatus('✅ Analyse médicale terminée', 'success');

    } catch (error) {
      console.error('Erreur analyse médicale:', error);
      updateMedicalStatus('❌ Erreur d\'analyse: ' + error.message, 'danger');
    }
  }

  function displayMedicalResults(data) {
    // Afficher les résultats
    medicalResults.style.display = 'block';

    // Diagnostic principal
    document.getElementById('primaryDiagnosis').textContent = data.primary_diagnosis || 'Non déterminé';
    
    // Confiance
    const confidence = Math.round((data.confidence || 0) * 100);
    document.getElementById('confidenceText').textContent = confidence + '%';
    document.getElementById('confidenceIndicator').style.width = confidence + '%';

    // Analyses détaillées
    if (data.detailed_pathology) {
      const pathology = data.detailed_pathology;
      
      // Sébum
      if (pathology.sebum_analysis) {
        document.getElementById('sebumGrade').textContent = pathology.sebum_analysis.sebum_grade || '---';
        document.getElementById('sebumRatio').textContent = 
          (pathology.sebum_analysis.sebum_ratio * 100).toFixed(1) + '%' || '---';
      }

      // Pores
      if (pathology.pore_morphology) {
        document.getElementById('poreGrade').textContent = pathology.pore_morphology.pore_grade || '---';
        document.getElementById('poreDensity').textContent = 
          pathology.pore_morphology.pore_density?.toFixed(1) || '---';
      }

      // Texture
      if (pathology.texture_pathology) {
        document.getElementById('textureGrade').textContent = pathology.texture_pathology.texture_grade || '---';
        document.getElementById('roughnessScore').textContent = 
          pathology.texture_pathology.roughness_score?.toFixed(2) || '---';
      }
    }

    // Notes cliniques
    if (data.clinical_notes && data.clinical_notes.length > 0) {
      const notesHtml = data.clinical_notes.map(note => 
        `<div class="clinical-note"><i class="fas fa-notes-medical"></i> ${note}</div>`
      ).join('');
      document.getElementById('clinicalNotes').innerHTML = notesHtml;
    }

    // Signes pathologiques
    if (data.pathological_findings && data.pathological_findings.length > 0) {
      const findingsHtml = data.pathological_findings.map(finding => 
        `<div class="pathology-finding"><i class="fas fa-exclamation-triangle"></i> ${finding}</div>`
      ).join('');
      document.getElementById('pathologicalFindings').innerHTML = findingsHtml;
    }

    // Scroll vers les résultats
    medicalResults.scrollIntoView({ behavior: 'smooth' });
  }

  function updateMedicalStatus(message, type) {
    medicalStatus.className = `alert alert-${type}`;
    medicalStatus.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
  }
});
</script>
{% endblock %}

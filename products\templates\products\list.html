{% extends 'skin_analysis/base.html' %}

{% block title %}Produits - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="text-white text-center mb-4">
                <i class="fas fa-shopping-bag"></i> Nos Produits
            </h2>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" style="border-radius: 15px;">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="brand" class="form-label">Marque</label>
                            <select name="brand" id="brand" class="form-select">
                                <option value="">Toutes les marques</option>
                                {% for brand in brands %}
                                <option value="{{ brand.id }}" {% if current_filters.brand == brand.id|stringformat:"s" %}selected{% endif %}>
                                    {{ brand.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="skin_type" class="form-label">Type de peau</label>
                            <select name="skin_type" id="skin_type" class="form-select">
                                <option value="">Tous les types</option>
                                {% for skin_type in skin_types %}
                                <option value="{{ skin_type.name }}" {% if current_filters.skin_type == skin_type.name %}selected{% endif %}>
                                    {{ skin_type.get_name_display }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter"></i> Filtrer
                            </button>
                            <a href="{% url 'products:list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Effacer
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Products Grid -->
    {% if page_obj %}
    <div class="row">
        {% for product in page_obj %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 15px; border: none; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: all 0.3s ease;">
                {% if product.image %}
                <img src="{{ product.image.url }}" class="card-img-top" style="height: 200px; object-fit: cover; border-radius: 15px 15px 0 0;" alt="{{ product.name }}">
                {% else %}
                <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px; border-radius: 15px 15px 0 0;">
                    <i class="fas fa-image fa-3x text-muted"></i>
                </div>
                {% endif %}
                
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">{{ product.name }}</h5>
                    <p class="text-muted small mb-2">
                        <i class="fas fa-building"></i> {{ product.brand.name }}
                    </p>
                    
                    <p class="card-text small flex-grow-1">{{ product.description|truncatewords:15 }}</p>
                    
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-tag"></i> {{ product.get_product_type_display }}
                        </small>
                        {% if product.volume %}
                        <small class="text-muted ms-2">
                            <i class="fas fa-flask"></i> {{ product.volume }}
                        </small>
                        {% endif %}
                    </div>
                    
                    <!-- Skin Types -->
                    <div class="mb-3">
                        {% for skin_type in product.suitable_skin_types.all %}
                        <span class="badge bg-primary me-1 mb-1">{{ skin_type.get_name_display }}</span>
                        {% endfor %}
                    </div>
                    
                    <!-- Benefits -->
                    {% if product.benefits %}
                    <div class="mb-3">
                        {% for benefit in product.benefits %}
                        <span class="badge bg-light text-dark me-1 mb-1">{{ benefit }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <!-- Price -->
                    {% if product.get_current_price %}
                    <div class="mb-3">
                        <span class="h5 text-success">
                            <i class="fas fa-tag"></i> {{ product.get_current_price|floatformat:3 }} TND
                        </span>
                    </div>
                    {% endif %}
                    
                    <!-- Actions -->
                    <div class="mt-auto">
                        <div class="d-grid gap-2">
                            <a href="{% url 'products:detail' product.id %}" class="btn btn-primary">
                                <i class="fas fa-eye"></i> Voir Détails
                            </a>
                            <div class="row g-2">
                                <div class="col-6">
                                    <a href="{% url 'price_tracking:product_history' product.id %}" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="fas fa-chart-line"></i> Prix
                                    </a>
                                </div>
                                {% if user.is_authenticated %}
                                <div class="col-6">
                                    <a href="{% url 'price_tracking:create_alert' product.id %}" class="btn btn-outline-warning btn-sm w-100">
                                        <i class="fas fa-bell"></i> Alerte
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Navigation des produits">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.brand %}&brand={{ request.GET.brand }}{% endif %}{% if request.GET.skin_type %}&skin_type={{ request.GET.skin_type }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.brand %}&brand={{ request.GET.brand }}{% endif %}{% if request.GET.skin_type %}&skin_type={{ request.GET.skin_type }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if request.GET.brand %}&brand={{ request.GET.brand }}{% endif %}{% if request.GET.skin_type %}&skin_type={{ request.GET.skin_type }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.brand %}&brand={{ request.GET.brand }}{% endif %}{% if request.GET.skin_type %}&skin_type={{ request.GET.skin_type }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.brand %}&brand={{ request.GET.brand }}{% endif %}{% if request.GET.skin_type %}&skin_type={{ request.GET.skin_type }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <!-- No Products -->
    <div class="row">
        <div class="col-12">
            <div class="card text-center" style="border-radius: 15px; background: rgba(255,255,255,0.95); padding: 60px 20px;">
                <i class="fas fa-search fa-5x text-muted mb-4"></i>
                <h3>Aucun Produit Trouvé</h3>
                <p class="text-muted mb-4">
                    Aucun produit ne correspond à vos critères de recherche.
                </p>
                <a href="{% url 'products:list' %}" class="btn btn-primary">
                    <i class="fas fa-refresh"></i> Voir Tous les Produits
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.form-select, .form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.page-link {
    border-radius: 10px;
    margin: 0 2px;
    border: none;
    color: #667eea;
}

.page-link:hover {
    background-color: #667eea;
    color: white;
}

.page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
}
</style>
{% endblock %}

from django.core.management.base import BaseCommand
from django.db import transaction
from skin_analysis.models import SkinType, SkinAnalysisQuestion
from products.models import Brand, ProductCategory, Product, ProductRecommendation
from price_tracking.models import Store, ProductPrice
from decimal import Decimal
import random

class Command(BaseCommand):
    help = 'Initialize database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Initializing database with sample data...')
        
        with transaction.atomic():
            self.create_skin_types()
            self.create_questions()
            self.create_brands()
            self.create_categories()
            self.create_stores()
            self.create_products()
            self.create_recommendations()
            self.create_sample_prices()
        
        self.stdout.write(self.style.SUCCESS('Database initialized successfully!'))

    def create_skin_types(self):
        """Créer les types de peau"""
        skin_types_data = [
            {
                'name': 'oily',
                'description': 'Peau grasse avec excès de sébum, pores dilatés et brillance.',
                'characteristics': {
                    'sebum_production': 'high',
                    'pore_size': 'large',
                    'shine': 'high',
                    'acne_prone': True
                },
                'care_tips': 'Utilisez des nettoyants doux, des toniques astringents et des hydratants légers non-comédogènes.'
            },
            {
                'name': 'dry',
                'description': 'Peau sèche avec manque d\'hydratation, tiraillements et desquamation.',
                'characteristics': {
                    'sebum_production': 'low',
                    'hydration': 'low',
                    'sensitivity': 'medium',
                    'tightness': True
                },
                'care_tips': 'Privilégiez les nettoyants crémeux, les sérums hydratants et les crèmes riches.'
            },
            {
                'name': 'combination',
                'description': 'Peau mixte avec zone T grasse et joues normales à sèches.',
                'characteristics': {
                    't_zone': 'oily',
                    'cheeks': 'normal_to_dry',
                    'complexity': 'high'
                },
                'care_tips': 'Adaptez votre routine selon les zones : produits matifiants sur la zone T, hydratants sur les joues.'
            },
            {
                'name': 'sensitive',
                'description': 'Peau sensible qui réagit facilement aux produits et facteurs externes.',
                'characteristics': {
                    'reactivity': 'high',
                    'redness': 'frequent',
                    'tolerance': 'low'
                },
                'care_tips': 'Choisissez des produits hypoallergéniques, sans parfum et testez toujours sur une petite zone.'
            },
            {
                'name': 'normal',
                'description': 'Peau normale, équilibrée, sans problèmes particuliers.',
                'characteristics': {
                    'balance': 'good',
                    'problems': 'minimal',
                    'maintenance': 'easy'
                },
                'care_tips': 'Maintenez l\'équilibre avec une routine simple : nettoyage, hydratation et protection solaire.'
            },
            {
                'name': 'acne_prone',
                'description': 'Peau à tendance acnéique avec boutons, points noirs et inflammation.',
                'characteristics': {
                    'acne': 'frequent',
                    'inflammation': 'present',
                    'scarring_risk': 'high'
                },
                'care_tips': 'Utilisez des produits anti-acné avec acide salicylique, évitez les produits comédogènes.'
            }
        ]
        
        for data in skin_types_data:
            skin_type, created = SkinType.objects.get_or_create(
                name=data['name'],
                defaults=data
            )
            if created:
                self.stdout.write(f'Created skin type: {skin_type.get_name_display()}')

    def create_questions(self):
        """Créer les questions d'analyse"""
        questions_data = [
            {
                'question_text': 'Comment décririez-vous votre peau le matin au réveil ?',
                'question_type': 'single_choice',
                'choices': ['Très grasse et brillante', 'Légèrement grasse', 'Normale', 'Légèrement sèche', 'Très sèche et tiraillée'],
                'weight': 2.0,
                'skin_type_mapping': {
                    'oily': ['Très grasse et brillante', 'Légèrement grasse'],
                    'dry': ['Très sèche et tiraillée', 'Légèrement sèche'],
                    'normal': ['Normale']
                },
                'order': 1
            },
            {
                'question_text': 'À quelle fréquence avez-vous des boutons ou points noirs ?',
                'question_type': 'single_choice',
                'choices': ['Très souvent (quotidiennement)', 'Souvent (plusieurs fois par semaine)', 'Parfois (quelques fois par mois)', 'Rarement', 'Jamais'],
                'weight': 1.8,
                'skin_type_mapping': {
                    'acne_prone': ['Très souvent (quotidiennement)', 'Souvent (plusieurs fois par semaine)'],
                    'oily': ['Parfois (quelques fois par mois)'],
                    'normal': ['Rarement', 'Jamais']
                },
                'order': 2
            },
            {
                'question_text': 'Comment votre peau réagit-elle aux nouveaux produits cosmétiques ?',
                'question_type': 'single_choice',
                'choices': ['Très sensible (rougeurs, irritations)', 'Sensible (légers picotements)', 'Normale (pas de réaction)', 'Très tolérante'],
                'weight': 1.5,
                'skin_type_mapping': {
                    'sensitive': ['Très sensible (rougeurs, irritations)', 'Sensible (légers picotements)'],
                    'normal': ['Normale (pas de réaction)', 'Très tolérante']
                },
                'order': 3
            },
            {
                'question_text': 'Votre zone T (front, nez, menton) est-elle différente de vos joues ?',
                'question_type': 'single_choice',
                'choices': ['Oui, zone T très grasse et joues sèches', 'Oui, zone T légèrement plus grasse', 'Non, tout mon visage est homogène'],
                'weight': 1.7,
                'skin_type_mapping': {
                    'combination': ['Oui, zone T très grasse et joues sèches', 'Oui, zone T légèrement plus grasse'],
                    'normal': ['Non, tout mon visage est homogène']
                },
                'order': 4
            },
            {
                'question_text': 'Comment se comporte votre peau en fin de journée ?',
                'question_type': 'single_choice',
                'choices': ['Très brillante', 'Légèrement brillante', 'Normale', 'Tiraillée', 'Très sèche'],
                'weight': 1.6,
                'skin_type_mapping': {
                    'oily': ['Très brillante', 'Légèrement brillante'],
                    'dry': ['Tiraillée', 'Très sèche'],
                    'normal': ['Normale']
                },
                'order': 5
            }
        ]
        
        for data in questions_data:
            question, created = SkinAnalysisQuestion.objects.get_or_create(
                question_text=data['question_text'],
                defaults=data
            )
            if created:
                self.stdout.write(f'Created question: {question.question_text[:50]}...')

    def create_brands(self):
        """Créer les marques tunisiennes"""
        brands_data = [
            {'name': 'Nuxe Tunisia', 'description': 'Marque française distribuée en Tunisie', 'is_tunisian': False},
            {'name': 'La Roche-Posay Tunisia', 'description': 'Dermocosmétique française disponible en Tunisie', 'is_tunisian': False},
            {'name': 'Vichy Tunisia', 'description': 'Soins thermaux français en Tunisie', 'is_tunisian': False},
            {'name': 'Eucerin Tunisia', 'description': 'Dermocosmétique allemande en Tunisie', 'is_tunisian': False},
            {'name': 'Avène Tunisia', 'description': 'Eau thermale française en Tunisie', 'is_tunisian': False},
            {'name': 'Bioderma Tunisia', 'description': 'Dermatologie française en Tunisie', 'is_tunisian': False},
            {'name': 'Pharmaceris Tunisia', 'description': 'Cosmétiques pharmaceutiques en Tunisie', 'is_tunisian': False},
            {'name': 'Sebamed Tunisia', 'description': 'Soins pH 5.5 en Tunisie', 'is_tunisian': False},
        ]
        
        for data in brands_data:
            brand, created = Brand.objects.get_or_create(
                name=data['name'],
                defaults=data
            )
            if created:
                self.stdout.write(f'Created brand: {brand.name}')

    def create_categories(self):
        """Créer les catégories de produits"""
        categories = [
            'Nettoyants', 'Toniques', 'Sérums', 'Hydratants', 
            'Protection Solaire', 'Traitements Anti-Acné', 'Masques', 
            'Exfoliants', 'Huiles', 'Contour des Yeux'
        ]
        
        for cat_name in categories:
            category, created = ProductCategory.objects.get_or_create(name=cat_name)
            if created:
                self.stdout.write(f'Created category: {category.name}')

    def create_stores(self):
        """Créer les magasins"""
        stores_data = [
            {'name': 'Pharmacie Centrale', 'is_online': False, 'is_physical': True, 'location': 'Avenue Habib Bourguiba, Tunis'},
            {'name': 'Parapharmacie Monoprix', 'is_online': False, 'is_physical': True, 'location': 'Centre-ville, Tunis'},
            {'name': 'Jumia Tunisia', 'website': 'https://jumia.com.tn', 'is_online': True, 'is_physical': False},
            {'name': 'Pharmacie en ligne TN', 'website': 'https://pharmacie-enligne.tn', 'is_online': True, 'is_physical': False},
            {'name': 'Carrefour Tunisia', 'is_online': True, 'is_physical': True, 'location': 'Plusieurs emplacements'},
        ]
        
        for data in stores_data:
            store, created = Store.objects.get_or_create(
                name=data['name'],
                defaults=data
            )
            if created:
                self.stdout.write(f'Created store: {store.name}')

    def create_products(self):
        """Créer des produits d'exemple"""
        brands = Brand.objects.all()
        categories = ProductCategory.objects.all()
        skin_types = SkinType.objects.all()

        products_data = [
            # Nettoyants
            {
                'name': 'Gel Nettoyant Purifiant',
                'brand': 'La Roche-Posay Tunisia',
                'category': 'Nettoyants',
                'product_type': 'cleanser',
                'description': 'Gel nettoyant doux pour peaux grasses et mixtes, élimine l\'excès de sébum sans dessécher.',
                'ingredients': 'Aqua, Sodium Laureth Sulfate, Coco-Betaine, Sodium Chloride, Zinc PCA',
                'suitable_skin_types': ['oily', 'combination', 'acne_prone'],
                'volume': '200ml',
                'usage_instructions': 'Appliquer matin et soir sur peau humide, masser délicatement, rincer à l\'eau tiède.',
                'benefits': ['Purifie', 'Matifie', 'Respecte le pH naturel']
            },
            {
                'name': 'Lait Nettoyant Hydratant',
                'brand': 'Avène Tunisia',
                'category': 'Nettoyants',
                'product_type': 'cleanser',
                'description': 'Lait nettoyant doux pour peaux sèches et sensibles, nourrit et apaise.',
                'ingredients': 'Avene Thermal Spring Water, Mineral Oil, Propylene Glycol, PEG-6 Stearate',
                'suitable_skin_types': ['dry', 'sensitive', 'normal'],
                'volume': '200ml',
                'usage_instructions': 'Appliquer sur peau sèche, masser délicatement, retirer avec un coton ou rincer.',
                'benefits': ['Hydrate', 'Apaise', 'Nettoie en douceur']
            },
            # Hydratants
            {
                'name': 'Crème Hydratante Matifiante',
                'brand': 'Vichy Tunisia',
                'category': 'Hydratants',
                'product_type': 'moisturizer',
                'description': 'Crème légère qui hydrate sans graisser, contrôle la brillance toute la journée.',
                'ingredients': 'Vichy Thermal Water, Dimethicone, Glycerin, Niacinamide, Zinc Gluconate',
                'suitable_skin_types': ['oily', 'combination'],
                'volume': '50ml',
                'usage_instructions': 'Appliquer matin et/ou soir sur peau propre et sèche.',
                'benefits': ['Hydrate 24h', 'Matifie', 'Resserre les pores']
            },
            {
                'name': 'Baume Réparateur Intense',
                'brand': 'Eucerin Tunisia',
                'category': 'Hydratants',
                'product_type': 'moisturizer',
                'description': 'Baume riche pour peaux très sèches, répare et protège la barrière cutanée.',
                'ingredients': 'Aqua, Glycerin, Petrolatum, Dimethicone, Ceramide NP, Hyaluronic Acid',
                'suitable_skin_types': ['dry', 'sensitive'],
                'volume': '75ml',
                'usage_instructions': 'Appliquer généreusement matin et soir sur peau propre.',
                'benefits': ['Répare', 'Nourrit intensément', 'Protège']
            },
            # Sérums
            {
                'name': 'Sérum Anti-Imperfections',
                'brand': 'Nuxe Tunisia',
                'category': 'Sérums',
                'product_type': 'serum',
                'description': 'Sérum concentré en acide salicylique pour réduire les imperfections.',
                'ingredients': 'Aqua, Niacinamide, Salicylic Acid, Zinc PCA, Hyaluronic Acid',
                'suitable_skin_types': ['oily', 'acne_prone', 'combination'],
                'volume': '30ml',
                'usage_instructions': 'Appliquer le soir sur peau propre, éviter le contour des yeux.',
                'benefits': ['Réduit les boutons', 'Resserre les pores', 'Lisse la peau']
            },
            {
                'name': 'Sérum Hydratant Intense',
                'brand': 'Bioderma Tunisia',
                'category': 'Sérums',
                'product_type': 'serum',
                'description': 'Sérum à l\'acide hyaluronique pour une hydratation profonde.',
                'ingredients': 'Aqua, Sodium Hyaluronate, Glycerin, Panthenol, Allantoin',
                'suitable_skin_types': ['dry', 'normal', 'sensitive'],
                'volume': '30ml',
                'usage_instructions': 'Appliquer matin et soir avant la crème hydratante.',
                'benefits': ['Hydrate en profondeur', 'Repulpe', 'Apaise']
            },
            # Protection solaire
            {
                'name': 'Fluide Solaire Matifiant SPF50+',
                'brand': 'La Roche-Posay Tunisia',
                'category': 'Protection Solaire',
                'product_type': 'sunscreen',
                'description': 'Protection solaire haute pour peaux grasses, fini mat longue durée.',
                'ingredients': 'Aqua, Diisopropyl Sebacate, Silica, Isopropyl Myristate, Ethylhexyl Salicylate',
                'suitable_skin_types': ['oily', 'combination', 'acne_prone'],
                'volume': '50ml',
                'usage_instructions': 'Appliquer généreusement 20 minutes avant exposition, renouveler toutes les 2h.',
                'benefits': ['Protection UVA/UVB', 'Matifie', 'Résistant à l\'eau']
            },
            # Traitements
            {
                'name': 'Gel Anti-Acné Purifiant',
                'brand': 'Pharmaceris Tunisia',
                'category': 'Traitements Anti-Acné',
                'product_type': 'treatment',
                'description': 'Gel de traitement intensif contre l\'acné et les points noirs.',
                'ingredients': 'Aqua, Salicylic Acid, Benzoyl Peroxide, Niacinamide, Zinc Oxide',
                'suitable_skin_types': ['acne_prone', 'oily'],
                'volume': '40ml',
                'usage_instructions': 'Appliquer localement sur les imperfections, 1 à 2 fois par jour.',
                'benefits': ['Élimine les boutons', 'Prévient les récidives', 'Purifie']
            }
        ]

        for product_data in products_data:
            try:
                brand = brands.get(name=product_data['brand'])
                category = categories.get(name=product_data['category'])

                product, created = Product.objects.get_or_create(
                    name=product_data['name'],
                    brand=brand,
                    defaults={
                        'category': category,
                        'product_type': product_data['product_type'],
                        'description': product_data['description'],
                        'ingredients': product_data['ingredients'],
                        'volume': product_data['volume'],
                        'usage_instructions': product_data['usage_instructions'],
                        'benefits': product_data['benefits']
                    }
                )

                if created:
                    # Ajouter les types de peau appropriés
                    for skin_type_name in product_data['suitable_skin_types']:
                        try:
                            skin_type = skin_types.get(name=skin_type_name)
                            product.suitable_skin_types.add(skin_type)
                        except SkinType.DoesNotExist:
                            continue

                    self.stdout.write(f'Created product: {product.name}')

            except (Brand.DoesNotExist, ProductCategory.DoesNotExist) as e:
                self.stdout.write(f'Error creating product {product_data["name"]}: {e}')

    def create_recommendations(self):
        """Créer des recommandations"""
        skin_types = SkinType.objects.all()
        products = Product.objects.all()

        recommendations_data = [
            # Peau grasse
            {
                'skin_type': 'oily',
                'product_name': 'Gel Nettoyant Purifiant',
                'priority': 1,
                'reason': 'Nettoie en profondeur et contrôle l\'excès de sébum sans agresser la peau.'
            },
            {
                'skin_type': 'oily',
                'product_name': 'Crème Hydratante Matifiante',
                'priority': 2,
                'reason': 'Hydrate légèrement tout en matifiant et contrôlant la brillance.'
            },
            {
                'skin_type': 'oily',
                'product_name': 'Sérum Anti-Imperfections',
                'priority': 3,
                'reason': 'Traite les imperfections et resserre les pores dilatés.'
            },
            {
                'skin_type': 'oily',
                'product_name': 'Fluide Solaire Matifiant SPF50+',
                'priority': 4,
                'reason': 'Protection solaire adaptée aux peaux grasses avec effet matifiant.'
            },
            # Peau sèche
            {
                'skin_type': 'dry',
                'product_name': 'Lait Nettoyant Hydratant',
                'priority': 1,
                'reason': 'Nettoie en douceur sans dessécher, préserve le film hydrolipidique.'
            },
            {
                'skin_type': 'dry',
                'product_name': 'Baume Réparateur Intense',
                'priority': 2,
                'reason': 'Nourrit intensément et répare la barrière cutanée endommagée.'
            },
            {
                'skin_type': 'dry',
                'product_name': 'Sérum Hydratant Intense',
                'priority': 3,
                'reason': 'Apporte une hydratation profonde et durable grâce à l\'acide hyaluronique.'
            },
            # Peau à tendance acnéique
            {
                'skin_type': 'acne_prone',
                'product_name': 'Gel Anti-Acné Purifiant',
                'priority': 1,
                'reason': 'Traitement ciblé contre l\'acné, réduit l\'inflammation et prévient les récidives.'
            },
            {
                'skin_type': 'acne_prone',
                'product_name': 'Gel Nettoyant Purifiant',
                'priority': 2,
                'reason': 'Nettoie en profondeur et élimine les impuretés sans irriter.'
            },
            {
                'skin_type': 'acne_prone',
                'product_name': 'Fluide Solaire Matifiant SPF50+',
                'priority': 3,
                'reason': 'Protection solaire non-comédogène, essentielle pendant les traitements anti-acné.'
            }
        ]

        for rec_data in recommendations_data:
            try:
                skin_type = skin_types.get(name=rec_data['skin_type'])
                product = products.get(name=rec_data['product_name'])

                recommendation, created = ProductRecommendation.objects.get_or_create(
                    skin_type=skin_type,
                    product=product,
                    defaults={
                        'priority': rec_data['priority'],
                        'reason': rec_data['reason'],
                        'created_by': 'system'
                    }
                )

                if created:
                    self.stdout.write(f'Created recommendation: {skin_type.get_name_display()} -> {product.name}')

            except (SkinType.DoesNotExist, Product.DoesNotExist) as e:
                self.stdout.write(f'Error creating recommendation: {e}')

    def create_sample_prices(self):
        """Créer des prix d'exemple"""
        products = Product.objects.all()
        stores = Store.objects.all()

        # Prix d'exemple pour chaque produit dans chaque magasin
        base_prices = {
            'Gel Nettoyant Purifiant': 45.0,
            'Lait Nettoyant Hydratant': 42.0,
            'Crème Hydratante Matifiante': 65.0,
            'Baume Réparateur Intense': 78.0,
            'Sérum Anti-Imperfections': 95.0,
            'Sérum Hydratant Intense': 89.0,
            'Fluide Solaire Matifiant SPF50+': 72.0,
            'Gel Anti-Acné Purifiant': 58.0,
        }

        for product in products:
            if product.name in base_prices:
                base_price = base_prices[product.name]

                for store in stores:
                    # Variation de prix selon le magasin
                    price_variation = random.uniform(0.85, 1.15)  # ±15%
                    final_price = Decimal(str(round(base_price * price_variation, 3)))

                    # Parfois en promotion
                    is_on_sale = random.choice([True, False, False, False])  # 25% de chance
                    original_price = None
                    discount_percentage = None

                    if is_on_sale:
                        original_price = final_price
                        discount_percentage = random.uniform(10, 30)
                        final_price = final_price * Decimal(str((100 - discount_percentage) / 100))

                    price, created = ProductPrice.objects.get_or_create(
                        product=product,
                        store=store,
                        defaults={
                            'price': final_price,
                            'is_on_sale': is_on_sale,
                            'original_price': original_price,
                            'discount_percentage': discount_percentage,
                            'availability': 'in_stock'
                        }
                    )

                    if created:
                        self.stdout.write(f'Created price: {product.name} - {final_price} TND chez {store.name}')

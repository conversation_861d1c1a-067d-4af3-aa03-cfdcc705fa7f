from django.core.management.base import BaseCommand
from django.db import transaction
from skin_analysis.models import SkinType, SkinAnalysisQuestion
from products.models import Brand, ProductCategory, Product, ProductRecommendation
from price_tracking.models import Store, ProductPrice
from decimal import Decimal
import random

class Command(BaseCommand):
    help = 'Initialize database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Initializing database with sample data...')
        
        with transaction.atomic():
            self.create_skin_types()
            self.create_questions()
            self.create_brands()
            self.create_categories()
            self.create_stores()
            self.create_products()
            self.create_recommendations()
            self.create_sample_prices()
        
        self.stdout.write(self.style.SUCCESS('Database initialized successfully!'))

    def create_skin_types(self):
        """Créer les types de peau"""
        skin_types_data = [
            {
                'name': 'oily',
                'description': 'Peau grasse avec excès de sébum, pores dilatés et brillance.',
                'characteristics': {
                    'sebum_production': 'high',
                    'pore_size': 'large',
                    'shine': 'high',
                    'acne_prone': True
                },
                'care_tips': 'Utilisez des nettoyants doux, des toniques astringents et des hydratants légers non-comédogènes.'
            },
            {
                'name': 'dry',
                'description': 'Peau sèche avec manque d\'hydratation, tiraillements et desquamation.',
                'characteristics': {
                    'sebum_production': 'low',
                    'hydration': 'low',
                    'sensitivity': 'medium',
                    'tightness': True
                },
                'care_tips': 'Privilégiez les nettoyants crémeux, les sérums hydratants et les crèmes riches.'
            },
            {
                'name': 'combination',
                'description': 'Peau mixte avec zone T grasse et joues normales à sèches.',
                'characteristics': {
                    't_zone': 'oily',
                    'cheeks': 'normal_to_dry',
                    'complexity': 'high'
                },
                'care_tips': 'Adaptez votre routine selon les zones : produits matifiants sur la zone T, hydratants sur les joues.'
            },
            {
                'name': 'sensitive',
                'description': 'Peau sensible qui réagit facilement aux produits et facteurs externes.',
                'characteristics': {
                    'reactivity': 'high',
                    'redness': 'frequent',
                    'tolerance': 'low'
                },
                'care_tips': 'Choisissez des produits hypoallergéniques, sans parfum et testez toujours sur une petite zone.'
            },
            {
                'name': 'normal',
                'description': 'Peau normale, équilibrée, sans problèmes particuliers.',
                'characteristics': {
                    'balance': 'good',
                    'problems': 'minimal',
                    'maintenance': 'easy'
                },
                'care_tips': 'Maintenez l\'équilibre avec une routine simple : nettoyage, hydratation et protection solaire.'
            },
            {
                'name': 'acne_prone',
                'description': 'Peau à tendance acnéique avec boutons, points noirs et inflammation.',
                'characteristics': {
                    'acne': 'frequent',
                    'inflammation': 'present',
                    'scarring_risk': 'high'
                },
                'care_tips': 'Utilisez des produits anti-acné avec acide salicylique, évitez les produits comédogènes.'
            }
        ]
        
        for data in skin_types_data:
            skin_type, created = SkinType.objects.get_or_create(
                name=data['name'],
                defaults=data
            )
            if created:
                self.stdout.write(f'Created skin type: {skin_type.get_name_display()}')

    def create_questions(self):
        """Créer les questions d'analyse"""
        questions_data = [
            {
                'question_text': 'Comment décririez-vous votre peau le matin au réveil ?',
                'question_type': 'single_choice',
                'choices': ['Très grasse et brillante', 'Légèrement grasse', 'Normale', 'Légèrement sèche', 'Très sèche et tiraillée'],
                'weight': 2.0,
                'skin_type_mapping': {
                    'oily': ['Très grasse et brillante', 'Légèrement grasse'],
                    'dry': ['Très sèche et tiraillée', 'Légèrement sèche'],
                    'normal': ['Normale']
                },
                'order': 1
            },
            {
                'question_text': 'À quelle fréquence avez-vous des boutons ou points noirs ?',
                'question_type': 'single_choice',
                'choices': ['Très souvent (quotidiennement)', 'Souvent (plusieurs fois par semaine)', 'Parfois (quelques fois par mois)', 'Rarement', 'Jamais'],
                'weight': 1.8,
                'skin_type_mapping': {
                    'acne_prone': ['Très souvent (quotidiennement)', 'Souvent (plusieurs fois par semaine)'],
                    'oily': ['Parfois (quelques fois par mois)'],
                    'normal': ['Rarement', 'Jamais']
                },
                'order': 2
            },
            {
                'question_text': 'Comment votre peau réagit-elle aux nouveaux produits cosmétiques ?',
                'question_type': 'single_choice',
                'choices': ['Très sensible (rougeurs, irritations)', 'Sensible (légers picotements)', 'Normale (pas de réaction)', 'Très tolérante'],
                'weight': 1.5,
                'skin_type_mapping': {
                    'sensitive': ['Très sensible (rougeurs, irritations)', 'Sensible (légers picotements)'],
                    'normal': ['Normale (pas de réaction)', 'Très tolérante']
                },
                'order': 3
            },
            {
                'question_text': 'Votre zone T (front, nez, menton) est-elle différente de vos joues ?',
                'question_type': 'single_choice',
                'choices': ['Oui, zone T très grasse et joues sèches', 'Oui, zone T légèrement plus grasse', 'Non, tout mon visage est homogène'],
                'weight': 1.7,
                'skin_type_mapping': {
                    'combination': ['Oui, zone T très grasse et joues sèches', 'Oui, zone T légèrement plus grasse'],
                    'normal': ['Non, tout mon visage est homogène']
                },
                'order': 4
            },
            {
                'question_text': 'Comment se comporte votre peau en fin de journée ?',
                'question_type': 'single_choice',
                'choices': ['Très brillante', 'Légèrement brillante', 'Normale', 'Tiraillée', 'Très sèche'],
                'weight': 1.6,
                'skin_type_mapping': {
                    'oily': ['Très brillante', 'Légèrement brillante'],
                    'dry': ['Tiraillée', 'Très sèche'],
                    'normal': ['Normale']
                },
                'order': 5
            }
        ]
        
        for data in questions_data:
            question, created = SkinAnalysisQuestion.objects.get_or_create(
                question_text=data['question_text'],
                defaults=data
            )
            if created:
                self.stdout.write(f'Created question: {question.question_text[:50]}...')

    def create_brands(self):
        """Créer les marques tunisiennes"""
        brands_data = [
            {'name': 'Nuxe Tunisia', 'description': 'Marque française distribuée en Tunisie', 'is_tunisian': False},
            {'name': 'La Roche-Posay Tunisia', 'description': 'Dermocosmétique française disponible en Tunisie', 'is_tunisian': False},
            {'name': 'Vichy Tunisia', 'description': 'Soins thermaux français en Tunisie', 'is_tunisian': False},
            {'name': 'Eucerin Tunisia', 'description': 'Dermocosmétique allemande en Tunisie', 'is_tunisian': False},
            {'name': 'Avène Tunisia', 'description': 'Eau thermale française en Tunisie', 'is_tunisian': False},
            {'name': 'Bioderma Tunisia', 'description': 'Dermatologie française en Tunisie', 'is_tunisian': False},
            {'name': 'Pharmaceris Tunisia', 'description': 'Cosmétiques pharmaceutiques en Tunisie', 'is_tunisian': False},
            {'name': 'Sebamed Tunisia', 'description': 'Soins pH 5.5 en Tunisie', 'is_tunisian': False},
        ]
        
        for data in brands_data:
            brand, created = Brand.objects.get_or_create(
                name=data['name'],
                defaults=data
            )
            if created:
                self.stdout.write(f'Created brand: {brand.name}')

    def create_categories(self):
        """Créer les catégories de produits"""
        categories = [
            'Nettoyants', 'Toniques', 'Sérums', 'Hydratants', 
            'Protection Solaire', 'Traitements Anti-Acné', 'Masques', 
            'Exfoliants', 'Huiles', 'Contour des Yeux'
        ]
        
        for cat_name in categories:
            category, created = ProductCategory.objects.get_or_create(name=cat_name)
            if created:
                self.stdout.write(f'Created category: {category.name}')

    def create_stores(self):
        """Créer les magasins"""
        stores_data = [
            {'name': 'Pharmacie Centrale', 'is_online': False, 'is_physical': True, 'location': 'Avenue Habib Bourguiba, Tunis'},
            {'name': 'Parapharmacie Monoprix', 'is_online': False, 'is_physical': True, 'location': 'Centre-ville, Tunis'},
            {'name': 'Jumia Tunisia', 'website': 'https://jumia.com.tn', 'is_online': True, 'is_physical': False},
            {'name': 'Pharmacie en ligne TN', 'website': 'https://pharmacie-enligne.tn', 'is_online': True, 'is_physical': False},
            {'name': 'Carrefour Tunisia', 'is_online': True, 'is_physical': True, 'location': 'Plusieurs emplacements'},
        ]
        
        for data in stores_data:
            store, created = Store.objects.get_or_create(
                name=data['name'],
                defaults=data
            )
            if created:
                self.stdout.write(f'Created store: {store.name}')

    def create_products(self):
        """Créer des produits d'exemple"""
        # Cette méthode sera étendue dans la prochaine partie
        self.stdout.write('Products creation will be implemented in next step...')

    def create_recommendations(self):
        """Créer des recommandations"""
        # Cette méthode sera étendue dans la prochaine partie
        self.stdout.write('Recommendations creation will be implemented in next step...')

    def create_sample_prices(self):
        """Créer des prix d'exemple"""
        # Cette méthode sera étendue dans la prochaine partie
        self.stdout.write('Sample prices creation will be implemented in next step...')

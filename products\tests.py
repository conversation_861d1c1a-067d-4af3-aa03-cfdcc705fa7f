from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
from .models import Brand, ProductCategory, Product, ProductRecommendation
from skin_analysis.models import SkinType

class ProductTestCase(TestCase):
    def setUp(self):
        """Configuration initiale pour les tests"""
        self.client = Client()

        # Créer une marque
        self.brand = Brand.objects.create(
            name='Test Brand',
            description='Marque de test',
            is_tunisian=True
        )

        # Créer une catégorie
        self.category = ProductCategory.objects.create(
            name='Nettoyants',
            description='Produits nettoyants'
        )

        # Créer un type de peau
        self.skin_type = SkinType.objects.create(
            name='oily',
            description='Peau grasse'
        )

        # Créer un produit
        self.product = Product.objects.create(
            name='Gel Nettoyant Test',
            brand=self.brand,
            category=self.category,
            product_type='cleanser',
            description='Gel nettoyant pour peau grasse',
            ingredients='Aqua, Sodium Laureth Sulfate',
            volume='200ml'
        )
        self.product.suitable_skin_types.add(self.skin_type)

    def test_product_creation(self):
        """Test de création d'un produit"""
        self.assertEqual(self.product.name, 'Gel Nettoyant Test')
        self.assertEqual(self.product.brand, self.brand)
        self.assertEqual(self.product.category, self.category)
        self.assertTrue(self.product.suitable_skin_types.filter(name='oily').exists())

    def test_product_list_view(self):
        """Test de la vue liste des produits"""
        response = self.client.get(reverse('products:list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Gel Nettoyant Test')

    def test_product_detail_view(self):
        """Test de la vue détail d'un produit"""
        response = self.client.get(reverse('products:detail', args=[self.product.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.product.name)

    def test_product_recommendations(self):
        """Test des recommandations de produits"""
        # Créer une recommandation
        recommendation = ProductRecommendation.objects.create(
            skin_type=self.skin_type,
            product=self.product,
            priority=1,
            reason='Parfait pour peau grasse'
        )

        response = self.client.get(reverse('products:recommendations', args=[self.skin_type.name]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.product.name)

    def test_brand_list_view(self):
        """Test de la vue liste des marques"""
        response = self.client.get(reverse('products:brands'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.brand.name)

    def test_product_filtering(self):
        """Test du filtrage des produits"""
        # Test filtrage par marque
        response = self.client.get(reverse('products:list'), {'brand': self.brand.id})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.product.name)

        # Test filtrage par type de peau
        response = self.client.get(reverse('products:list'), {'skin_type': self.skin_type.name})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.product.name)

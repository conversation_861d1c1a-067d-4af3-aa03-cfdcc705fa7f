"""
Commande Django pour importer un dataset personnalisé
Usage: python manage.py import_custom_dataset --dataset-path /path/to/dataset --name mon_dataset
"""

import os
import json
import shutil
from django.core.management.base import BaseCommand
from django.conf import settings
from skin_analysis.dataset_downloader import dataset_downloader
from PIL import Image
import numpy as np

class Command(BaseCommand):
    help = 'Importe un dataset personnalisé dans le système'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dataset-path',
            type=str,
            required=True,
            help='Chemin vers votre dataset'
        )
        parser.add_argument(
            '--name',
            type=str,
            required=True,
            help='Nom du dataset (ex: tunisia_dermatology)'
        )
        parser.add_argument(
            '--validate',
            action='store_true',
            help='Valider le dataset avant import'
        )
        parser.add_argument(
            '--copy-files',
            action='store_true',
            help='Copier les fichiers (sinon liens symboliques)'
        )

    def handle(self, *args, **options):
        dataset_path = options['dataset_path']
        dataset_name = options['name']
        
        self.stdout.write(self.style.SUCCESS(f'📥 Import du dataset: {dataset_name}'))
        self.stdout.write(f'📁 Source: {dataset_path}')
        
        # Vérifier que le dataset existe
        if not os.path.exists(dataset_path):
            self.stdout.write(self.style.ERROR(f'❌ Dataset non trouvé: {dataset_path}'))
            return
        
        # Valider le dataset si demandé
        if options['validate']:
            if not self.validate_dataset(dataset_path):
                self.stdout.write(self.style.ERROR('❌ Validation échouée'))
                return
        
        # Importer le dataset
        success = self.import_dataset(dataset_path, dataset_name, options['copy_files'])
        
        if success:
            self.stdout.write(self.style.SUCCESS(f'✅ Dataset {dataset_name} importé avec succès'))
            self.stdout.write(f'🚀 Pour entraîner: python manage.py train_ml_model --dataset-path {dataset_name}')
        else:
            self.stdout.write(self.style.ERROR('❌ Échec de l\'import'))

    def validate_dataset(self, dataset_path):
        """Valide la structure et le contenu du dataset"""
        self.stdout.write('🔍 Validation du dataset...')
        
        # Vérifier annotations.json
        annotations_file = os.path.join(dataset_path, 'annotations.json')
        if not os.path.exists(annotations_file):
            self.stdout.write(self.style.ERROR('❌ Fichier annotations.json manquant'))
            return False
        
        try:
            with open(annotations_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Erreur lecture annotations.json: {e}'))
            return False
        
        # Vérifier la structure des annotations
        required_fields = ['filename', 'skin_type']
        skin_types = ['normal', 'oily', 'dry', 'combination', 'sensitive', 'acne_prone']
        
        valid_count = 0
        error_count = 0
        type_counts = {skin_type: 0 for skin_type in skin_types}
        
        for i, annotation in enumerate(annotations):
            # Vérifier les champs requis
            for field in required_fields:
                if field not in annotation:
                    self.stdout.write(f'⚠️  Annotation {i}: champ {field} manquant')
                    error_count += 1
                    continue
            
            # Vérifier le type de peau
            skin_type = annotation['skin_type']
            if skin_type not in skin_types:
                self.stdout.write(f'⚠️  Annotation {i}: type de peau invalide: {skin_type}')
                error_count += 1
                continue
            
            # Vérifier que le fichier existe
            image_path = os.path.join(dataset_path, annotation['filename'])
            if not os.path.exists(image_path):
                self.stdout.write(f'⚠️  Image manquante: {annotation["filename"]}')
                error_count += 1
                continue
            
            # Vérifier que c'est une image valide
            try:
                with Image.open(image_path) as img:
                    img.verify()
                valid_count += 1
                type_counts[skin_type] += 1
            except Exception as e:
                self.stdout.write(f'⚠️  Image corrompue {annotation["filename"]}: {e}')
                error_count += 1
        
        # Afficher les statistiques
        self.stdout.write(f'📊 Images valides: {valid_count}')
        self.stdout.write(f'❌ Erreurs: {error_count}')
        
        self.stdout.write('📈 Répartition par type:')
        for skin_type, count in type_counts.items():
            if count > 0:
                self.stdout.write(f'  - {skin_type}: {count} images')
        
        # Recommandations
        min_per_type = 50
        insufficient_types = [t for t, c in type_counts.items() if c < min_per_type and c > 0]
        
        if insufficient_types:
            self.stdout.write(self.style.WARNING(f'⚠️  Types avec peu d\'images (< {min_per_type}): {insufficient_types}'))
            self.stdout.write('💡 Recommandation: Collectez plus d\'images pour ces types')
        
        # Validation réussie si au moins 80% des images sont valides
        success_rate = valid_count / (valid_count + error_count) if (valid_count + error_count) > 0 else 0
        
        if success_rate >= 0.8:
            self.stdout.write(self.style.SUCCESS(f'✅ Validation réussie ({success_rate:.1%} de réussite)'))
            return True
        else:
            self.stdout.write(self.style.ERROR(f'❌ Validation échouée ({success_rate:.1%} de réussite, minimum 80%)'))
            return False

    def import_dataset(self, source_path, dataset_name, copy_files=False):
        """Importe le dataset dans le système"""
        try:
            # Créer le dossier de destination
            dest_path = os.path.join(dataset_downloader.base_path, dataset_name)
            
            if os.path.exists(dest_path):
                self.stdout.write(f'⚠️  Dataset {dataset_name} existe déjà, écrasement...')
                shutil.rmtree(dest_path)
            
            os.makedirs(dest_path, exist_ok=True)
            
            # Copier ou lier les fichiers
            if copy_files:
                self.stdout.write('📁 Copie des fichiers...')
                shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
            else:
                self.stdout.write('🔗 Création de liens symboliques...')
                # Copier annotations.json
                shutil.copy2(
                    os.path.join(source_path, 'annotations.json'),
                    os.path.join(dest_path, 'annotations.json')
                )
                
                # Créer liens pour les dossiers d'images
                for item in os.listdir(source_path):
                    source_item = os.path.join(source_path, item)
                    dest_item = os.path.join(dest_path, item)
                    
                    if os.path.isdir(source_item) and item != '__pycache__':
                        try:
                            os.symlink(source_item, dest_item)
                        except OSError:
                            # Fallback: copier si les liens symboliques ne fonctionnent pas
                            shutil.copytree(source_item, dest_item)
            
            # Mettre à jour la liste des datasets disponibles
            self.update_dataset_registry(dataset_name, dest_path)
            
            return True
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Erreur import: {e}'))
            return False

    def update_dataset_registry(self, dataset_name, dataset_path):
        """Met à jour le registre des datasets"""
        try:
            # Charger les statistiques du dataset
            annotations_file = os.path.join(dataset_path, 'annotations.json')
            with open(annotations_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)
            
            # Calculer les statistiques
            total_images = len(annotations)
            skin_types = {}
            for annotation in annotations:
                skin_type = annotation['skin_type']
                skin_types[skin_type] = skin_types.get(skin_type, 0) + 1
            
            # Ajouter au registre des datasets
            dataset_downloader.datasets_info[dataset_name] = {
                'name': dataset_name,
                'description': f'Dataset personnalisé ({total_images} images)',
                'total_images': total_images,
                'skin_types': skin_types,
                'path': dataset_path,
                'type': 'custom',
                'imported_date': str(np.datetime64('now'))
            }
            
            self.stdout.write(f'📋 Dataset enregistré: {total_images} images')
            
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'⚠️  Erreur mise à jour registre: {e}'))

"""
Commande pour tester l'analyseur hospitalier
"""

import os
from django.core.management.base import BaseCommand
from skin_analysis.hospital_grade_analyzer import hospital_analyzer

class Command(BaseCommand):
    help = 'Test l\'analyseur dermatologique de grade hospitalier'

    def add_arguments(self, parser):
        parser.add_argument(
            '--image-path',
            type=str,
            help='Chemin vers une image spécifique à tester'
        )
        parser.add_argument(
            '--test-dataset',
            action='store_true',
            help='Tester sur quelques images du dataset'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🏥 Test de l\'Analyseur Hospitalier'))
        
        if options['image_path']:
            self.test_single_image(options['image_path'])
        elif options['test_dataset']:
            self.test_dataset_images()
        else:
            self.stdout.write(self.style.WARNING('Utilisez --image-path ou --test-dataset'))
    
    def test_single_image(self, image_path):
        """Test une image spécifique"""
        if not os.path.exists(image_path):
            self.stdout.write(self.style.ERROR(f'❌ Image non trouvée: {image_path}'))
            return
        
        self.stdout.write(f'🔬 Analyse hospitalière de: {image_path}')
        
        result = hospital_analyzer.analyze_hospital_grade(image_path)
        
        if result:
            self.stdout.write(self.style.SUCCESS('✅ Analyse hospitalière réussie'))
            self.stdout.write(f'🎯 Diagnostic: {result["primary_diagnosis"]}')
            self.stdout.write(f'📊 Confiance: {result["confidence"]:.1%}')
            self.stdout.write(f'🏥 Grade: {result.get("medical_grade", "N/A")}')
            
            # Afficher les analyses détaillées
            if 'detailed_pathology' in result:
                pathology = result['detailed_pathology']
                self.stdout.write('\n🔍 Analyses pathologiques:')
                
                if 'sebum_analysis' in pathology:
                    sebum = pathology['sebum_analysis']
                    self.stdout.write(f'  💧 Sébum: {sebum["sebum_grade"]} (ratio: {sebum["sebum_ratio"]:.3f})')
                
                if 'pore_morphology' in pathology:
                    pore = pathology['pore_morphology']
                    self.stdout.write(f'  🕳️  Pores: {pore["pore_grade"]} (densité: {pore["pore_density"]:.2f})')
                
                if 'texture_pathology' in pathology:
                    texture = pathology['texture_pathology']
                    self.stdout.write(f'  🌊 Texture: {texture["texture_grade"]} (rugosité: {texture["roughness_score"]:.3f})')
            
            # Notes cliniques
            if 'clinical_notes' in result and result['clinical_notes']:
                self.stdout.write('\n📋 Notes cliniques:')
                for note in result['clinical_notes']:
                    self.stdout.write(f'  • {note}')
            
            # Signes pathologiques
            if 'pathological_findings' in result and result['pathological_findings']:
                self.stdout.write('\n⚠️  Signes pathologiques:')
                for finding in result['pathological_findings']:
                    self.stdout.write(f'  • {finding}')
        else:
            self.stdout.write(self.style.ERROR('❌ Analyse hospitalière échouée'))
    
    def test_dataset_images(self):
        """Test quelques images du dataset"""
        dataset_path = 'media/real_datasets/synthetic_demo'
        
        if not os.path.exists(dataset_path):
            self.stdout.write(self.style.ERROR(f'❌ Dataset non trouvé: {dataset_path}'))
            return
        
        # Tester quelques images de chaque type
        skin_types = ['normal', 'oily', 'dry']
        
        for skin_type in skin_types:
            type_path = os.path.join(dataset_path, skin_type)
            if os.path.exists(type_path):
                images = [f for f in os.listdir(type_path) if f.endswith(('.jpg', '.jpeg', '.png'))]
                if images:
                    # Tester la première image de ce type
                    test_image = os.path.join(type_path, images[0])
                    self.stdout.write(f'\n🧪 Test hospitalier {skin_type}: {images[0]}')
                    
                    result = hospital_analyzer.analyze_hospital_grade(test_image)
                    
                    if result:
                        confidence = result['confidence']
                        diagnosis = result['primary_diagnosis']
                        
                        if confidence >= 0.95:
                            status = self.style.SUCCESS(f'✅ {diagnosis} ({confidence:.1%})')
                        elif confidence >= 0.90:
                            status = self.style.WARNING(f'⚠️  {diagnosis} ({confidence:.1%})')
                        else:
                            status = self.style.ERROR(f'❌ {diagnosis} ({confidence:.1%})')
                        
                        self.stdout.write(f'  {status}')
                        
                        # Afficher les marqueurs pathologiques
                        pathology = result.get('detailed_pathology', {})
                        pathological_markers = []
                        
                        for analysis_type, analysis_data in pathology.items():
                            if isinstance(analysis_data, dict) and analysis_data.get('pathological_markers'):
                                pathological_markers.append(analysis_type)
                        
                        if pathological_markers:
                            self.stdout.write(f'    🔬 Marqueurs pathologiques: {", ".join(pathological_markers)}')
                    else:
                        self.stdout.write(self.style.ERROR(f'  ❌ Échec analyse'))
        
        self.stdout.write(self.style.SUCCESS('\n🎯 Test hospitalier terminé'))

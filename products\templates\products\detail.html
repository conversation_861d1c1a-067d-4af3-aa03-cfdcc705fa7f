{% extends 'skin_analysis/base.html' %}

{% block title %}{{ product.name }} - SkinCare Tunisia{% endblock %}

{% block content %}
<div class="container mt-5 pt-5">
    <div class="row">
        <div class="col-lg-6">
            {% if product.image %}
            <img src="{{ product.image.url }}" class="img-fluid rounded" alt="{{ product.name }}">
            {% else %}
            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 400px;">
                <i class="fas fa-image fa-5x text-muted"></i>
            </div>
            {% endif %}
        </div>
        
        <div class="col-lg-6">
            <div class="card" style="border-radius: 15px;">
                <div class="card-body">
                    <h1>{{ product.name }}</h1>
                    <p class="text-muted h5 mb-3">
                        <i class="fas fa-building"></i> {{ product.brand.name }}
                    </p>
                    
                    <p class="lead">{{ product.description }}</p>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <strong>Type:</strong> {{ product.get_product_type_display }}
                        </div>
                        {% if product.volume %}
                        <div class="col-6">
                            <strong>Volume:</strong> {{ product.volume }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <strong>Convient aux types de peau:</strong><br>
                        {% for skin_type in product.suitable_skin_types.all %}
                        <span class="badge bg-primary me-1">{{ skin_type.get_name_display }}</span>
                        {% endfor %}
                    </div>
                    
                    {% if product.benefits %}
                    <div class="mb-3">
                        <strong>Bénéfices:</strong><br>
                        {% for benefit in product.benefits %}
                        <span class="badge bg-success me-1 mb-1">{{ benefit }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    {% if product.get_current_price %}
                    <div class="mb-4">
                        <h3 class="text-success">
                            <i class="fas fa-tag"></i> {{ product.get_current_price|floatformat:3 }} TND
                        </h3>
                    </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'price_tracking:product_history' product.id %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-chart-line"></i> Voir l'Historique des Prix
                        </a>
                        {% if user.is_authenticated %}
                        <a href="{% url 'price_tracking:create_alert' product.id %}" class="btn btn-warning">
                            <i class="fas fa-bell"></i> Créer une Alerte de Prix
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Product Details -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card" style="border-radius: 15px;">
                <div class="card-body">
                    <ul class="nav nav-tabs" id="productTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="ingredients-tab" data-bs-toggle="tab" data-bs-target="#ingredients" type="button" role="tab">
                                <i class="fas fa-flask"></i> Ingrédients
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="usage-tab" data-bs-toggle="tab" data-bs-target="#usage" type="button" role="tab">
                                <i class="fas fa-info-circle"></i> Mode d'Emploi
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content mt-3" id="productTabsContent">
                        <div class="tab-pane fade show active" id="ingredients" role="tabpanel">
                            <h5>Composition</h5>
                            <p>{{ product.ingredients }}</p>
                        </div>
                        <div class="tab-pane fade" id="usage" role="tabpanel">
                            <h5>Instructions d'Utilisation</h5>
                            <p>{{ product.usage_instructions|default:"Instructions non disponibles." }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Similar Products -->
    {% if similar_products %}
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-white mb-4">Produits Similaires</h3>
            <div class="row">
                {% for similar in similar_products %}
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100" style="border-radius: 15px;">
                        {% if similar.image %}
                        <img src="{{ similar.image.url }}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="{{ similar.name }}">
                        {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 150px;">
                            <i class="fas fa-image fa-2x text-muted"></i>
                        </div>
                        {% endif %}
                        
                        <div class="card-body">
                            <h6 class="card-title">{{ similar.name }}</h6>
                            <p class="text-muted small">{{ similar.brand.name }}</p>
                            <a href="{% url 'products:detail' similar.id %}" class="btn btn-outline-primary btn-sm">
                                Voir Détails
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

#!/usr/bin/env python3
"""
Script de configuration automatique pour SkinCare Tunisia
Installe les dépendances et configure l'application
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """Exécute une commande et affiche le résultat"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Erreur: {e}")
        print(f"Sortie d'erreur: {e.stderr}")
        return False

def check_python_version():
    """Vérifie la version de Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ requis. Version actuelle:", f"{version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} détecté")
    return True

def install_dependencies():
    """Installe les dépendances Python"""
    dependencies = [
        "django",
        "pillow", 
        "opencv-python",
        "numpy",
        "tensorflow",
        "scikit-learn",
        "matplotlib",
        "seaborn", 
        "pandas",
        "requests",
        "beautifulsoup4"
    ]
    
    print("\n📦 Installation des dépendances...")
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"Installation de {dep}"):
            print(f"⚠️  Erreur lors de l'installation de {dep}, mais on continue...")
    
    return True

def setup_django():
    """Configure Django"""
    commands = [
        ("python manage.py makemigrations", "Création des migrations"),
        ("python manage.py migrate", "Application des migrations"),
        ("python manage.py init_data", "Initialisation des données de test"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True

def create_superuser():
    """Propose de créer un superutilisateur"""
    response = input("\n👤 Voulez-vous créer un compte administrateur ? (y/N): ")
    if response.lower() in ['y', 'yes', 'o', 'oui']:
        print("\n📝 Création du compte administrateur...")
        print("Suivez les instructions ci-dessous:")
        os.system("python manage.py createsuperuser")

def main():
    """Fonction principale"""
    print("🌟 Configuration de SkinCare Tunisia")
    print("=" * 50)
    
    # Vérifier Python
    if not check_python_version():
        sys.exit(1)
    
    # Vérifier que nous sommes dans le bon répertoire
    if not os.path.exists("manage.py"):
        print("❌ Fichier manage.py non trouvé. Assurez-vous d'être dans le répertoire du projet.")
        sys.exit(1)
    
    # Installer les dépendances
    if not install_dependencies():
        print("❌ Erreur lors de l'installation des dépendances")
        sys.exit(1)
    
    # Configurer Django
    if not setup_django():
        print("❌ Erreur lors de la configuration de Django")
        sys.exit(1)
    
    # Créer un superutilisateur
    create_superuser()
    
    print("\n🎉 Configuration terminée avec succès!")
    print("\n🚀 Pour démarrer l'application:")
    print("   python manage.py runserver")
    print("\n🌐 L'application sera accessible à:")
    print("   http://127.0.0.1:8000/")
    
    # Proposer de démarrer automatiquement
    response = input("\n▶️  Voulez-vous démarrer l'application maintenant ? (y/N): ")
    if response.lower() in ['y', 'yes', 'o', 'oui']:
        print("\n🚀 Démarrage du serveur...")
        os.system("python manage.py runserver")

if __name__ == "__main__":
    main()
